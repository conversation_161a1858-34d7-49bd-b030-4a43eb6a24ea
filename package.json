{"name": "disaster-reporting-platform", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "node_modules/.bin/vite", "build": "node_modules/.bin/vite build", "lint": "eslint .", "preview": "node_modules/.bin/vite preview"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.1", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "recharts": "^2.12.7", "date-fns": "^3.6.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/leaflet": "^1.9.12", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}