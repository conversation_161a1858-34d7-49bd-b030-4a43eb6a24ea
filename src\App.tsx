import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from './context/AuthContext';
import { AdminProvider } from './context/AdminContext';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import Header from './components/Layout/Header';
import Footer from './components/Layout/Footer';
import Home from './pages/Home';
import Reports from './pages/Reports';
import ReportDetail from './pages/ReportDetail';
import CreateReport from './pages/CreateReport';
import Dashboard from './pages/Dashboard';
import Partners from './pages/Partners';
import Resources from './pages/Resources';
import NotificationDemo from './pages/NotificationDemo';

// Admin Components
import AdminLogin from './components/Admin/AdminLogin';
import AdminLayout from './components/Admin/AdminLayout';
import AdminDashboard from './pages/Admin/AdminDashboard';
import ContentManagement from './pages/Admin/ContentManagement';
import UserManagement from './pages/Admin/UserManagement';
import AdminAnalytics from './pages/Admin/AdminAnalytics';
import SystemReport from './pages/Admin/SystemReport';

// Editor Components
import EditorLayout from './components/Editor/EditorLayout';
import EditorDashboard from './pages/Editor/EditorDashboard';

// Role-based redirect component
const RoleBasedRedirect: React.FC = () => {
  const { user, getRedirectPath } = useAuth();
  
  if (!user) {
    return <Navigate to="/" replace />;
  }
  
  return <Navigate to={getRedirectPath()} replace />;
};

// Admin Routes Component with Strict Protection
const AdminRoutes: React.FC = () => {
  return (
    <ProtectedRoute adminOnly={true}>
      <AdminProvider>
        <AdminLayout>
          <Routes>
            <Route index element={<AdminDashboard />} />
            <Route 
              path="content" 
              element={
                <ProtectedRoute requiredPermission="admin.content.manage">
                  <ContentManagement />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="users" 
              element={
                <ProtectedRoute requiredPermission="admin.users.manage">
                  <UserManagement />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="analytics" 
              element={
                <ProtectedRoute requiredPermission="admin.analytics.view">
                  <AdminAnalytics />
                </ProtectedRoute>
              } 
            />
            <Route 
              path="system-report" 
              element={
                <ProtectedRoute requiredPermission="admin.system.report">
                  <SystemReport />
                </ProtectedRoute>
              } 
            />
          </Routes>
        </AdminLayout>
      </AdminProvider>
    </ProtectedRoute>
  );
};

// Editor Routes Component with Strict Protection
const EditorRoutes: React.FC = () => {
  return (
    <ProtectedRoute editorOnly={true}>
      <EditorLayout>
        <Routes>
          <Route index element={<EditorDashboard />} />
          <Route 
            path="content" 
            element={
              <ProtectedRoute requiredPermission="editor.content.manage">
                <div className="p-6">
                  <h1 className="text-2xl font-bold text-gray-900">Content Management</h1>
                  <p className="text-gray-600 mt-2">Editor content management interface</p>
                </div>
              </ProtectedRoute>
            } 
          />
          <Route 
            path="reports" 
            element={
              <ProtectedRoute requiredPermission="editor.reports.moderate">
                <div className="p-6">
                  <h1 className="text-2xl font-bold text-gray-900">Report Moderation</h1>
                  <p className="text-gray-600 mt-2">Moderate and review user reports</p>
                </div>
              </ProtectedRoute>
            } 
          />
          <Route 
            path="analytics" 
            element={
              <ProtectedRoute requiredPermission="editor.analytics.view">
                <div className="p-6">
                  <h1 className="text-2xl font-bold text-gray-900">Editor Analytics</h1>
                  <p className="text-gray-600 mt-2">Content performance and engagement metrics</p>
                </div>
              </ProtectedRoute>
            } 
          />
        </Routes>
      </EditorLayout>
    </ProtectedRoute>
  );
};

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Admin Login Route (accessible to all) */}
          <Route path="/admin/login" element={<AdminLogin />} />
          
          {/* Protected Admin Routes - ADMIN ONLY */}
          <Route path="/admin/*" element={<AdminRoutes />} />

          {/* Protected Editor Routes - EDITOR ONLY */}
          <Route path="/editor/*" element={<EditorRoutes />} />

          {/* Role-based redirect route */}
          <Route path="/redirect" element={<RoleBasedRedirect />} />

          {/* Public Routes with Header/Footer - USER ACCESS */}
          <Route path="/*" element={
            <div className="min-h-screen flex flex-col">
              <Header />
              <main className="flex-1">
                <Routes>
                  <Route path="/" element={<Home />} />
                  <Route 
                    path="/reports" 
                    element={
                      <ProtectedRoute requiredPermission="reports.view">
                        <Reports />
                      </ProtectedRoute>
                    } 
                  />
                  <Route 
                    path="/reports/:reportId" 
                    element={
                      <ProtectedRoute requiredPermission="reports.view">
                        <ReportDetail />
                      </ProtectedRoute>
                    } 
                  />
                  <Route 
                    path="/report/new" 
                    element={
                      <ProtectedRoute requiredPermission="reports.create">
                        <CreateReport />
                      </ProtectedRoute>
                    } 
                  />
                  <Route 
                    path="/dashboard" 
                    element={
                      <ProtectedRoute userOnly={true}>
                        <Dashboard />
                      </ProtectedRoute>
                    } 
                  />
                  <Route path="/partners" element={<Partners />} />
                  <Route path="/resources" element={<Resources />} />
                  <Route path="/notifications" element={<NotificationDemo />} />
                </Routes>
              </main>
              <Footer />
            </div>
          } />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;