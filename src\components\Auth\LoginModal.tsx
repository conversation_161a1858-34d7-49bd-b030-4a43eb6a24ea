import React, { useState } from 'react';
import { X, Mail, Lock, Loader, AlertTriangle, Eye, EyeOff } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const LoginModal: React.FC<LoginModalProps> = ({ isOpen, onClose }) => {
  const { login, isLoading } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [selectedUserType, setSelectedUserType] = useState<'user' | 'editor' | 'admin'>('user');

  // Demo credentials for different user types with strict role separation
  const demoCredentials = {
    user: {
      email: '<EMAIL>',
      password: 'password123',
      name: 'Regular User',
      description: 'Access to user dashboard, can create reports and offer assistance',
      redirectPath: '/dashboard'
    },
    editor: {
      email: '<EMAIL>',
      password: 'editor123',
      name: 'Content Editor',
      description: 'Access to editor panel, can manage content and moderate reports',
      redirectPath: '/editor'
    },
    admin: {
      email: '<EMAIL>',
      password: 'admin123',
      name: 'System Administrator',
      description: 'Access to admin panel, full system control and management',
      redirectPath: '/admin'
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    try {
      await login(email, password);
      
      // Close modal and let the auth system handle redirection
      onClose();
      
      // Small delay to ensure auth context is updated
      setTimeout(() => {
        // The Header component will handle automatic redirection based on role
        window.location.reload(); // Force reload to ensure proper routing
      }, 100);
      
    } catch (error) {
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError('Login failed. Please try again.');
      }
    }
  };

  const handleDemoLogin = (userType: 'user' | 'editor' | 'admin') => {
    const credentials = demoCredentials[userType];
    setEmail(credentials.email);
    setPassword(credentials.password);
    setSelectedUserType(userType);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900">Welcome Back</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} className="text-gray-400" />
          </button>
        </div>

        <div className="p-6">
          {/* Role-Based Access Notice */}
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
            <h3 className="font-medium text-blue-900 mb-2">🔐 Role-Based Access System</h3>
            <p className="text-sm text-blue-800 leading-relaxed">
              Each user type has exclusive access to their designated interface. You'll be automatically redirected to your role-specific dashboard upon login.
            </p>
          </div>

          {/* Demo User Type Selector */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Select Account Type</h3>
            <div className="grid grid-cols-1 gap-3">
              {Object.entries(demoCredentials).map(([type, creds]) => (
                <button
                  key={type}
                  onClick={() => handleDemoLogin(type as 'user' | 'editor' | 'admin')}
                  className={`p-4 text-left border-2 rounded-xl transition-all duration-200 hover:shadow-sm ${
                    selectedUserType === type
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full border-2 ${
                        selectedUserType === type
                          ? 'border-blue-500 bg-blue-500'
                          : 'border-gray-300'
                      }`} />
                      <div className="font-medium text-gray-900 capitalize">{creds.name}</div>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      type === 'admin' ? 'bg-red-100 text-red-700' :
                      type === 'editor' ? 'bg-green-100 text-green-700' :
                      'bg-blue-100 text-blue-700'
                    }`}>
                      {type.toUpperCase()}
                    </span>
                  </div>
                  <div className="text-xs text-gray-600 leading-relaxed">{creds.description}</div>
                  <div className="text-xs text-gray-500 mt-2 font-mono">
                    → Redirects to: {creds.redirectPath}
                  </div>
                </button>
              ))}
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-5">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <div className="relative">
                <Mail size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  placeholder="Enter your email"
                  required
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <div className="relative">
                <Lock size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pl-10 pr-12 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  placeholder="Enter your password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
            </div>

            {error && (
              <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-xl">
                <AlertTriangle size={16} className="text-red-600 flex-shrink-0" />
                <span className="text-sm text-red-700">{error}</span>
              </div>
            )}

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm"
            >
              {isLoading ? (
                <>
                  <Loader size={20} className="animate-spin" />
                  <span>Signing in...</span>
                </>
              ) : (
                <span>Sign In & Redirect</span>
              )}
            </button>
          </form>

          {/* Current Demo Credentials Display */}
          <div className="mt-6 pt-6 border-t border-gray-100">
            <div className="bg-gray-50 rounded-xl p-4">
              <h4 className="font-medium text-gray-900 mb-2">Current Demo Credentials</h4>
              <div className="text-sm text-gray-700 space-y-1">
                <p><strong>Email:</strong> {email}</p>
                <p><strong>Password:</strong> {password}</p>
                <p><strong>Role:</strong> {selectedUserType.toUpperCase()}</p>
                <p className="text-xs text-gray-600 mt-2">
                  {demoCredentials[selectedUserType].description}
                </p>
              </div>
            </div>
          </div>

          {/* Access Control Notice */}
          <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-xl">
            <div className="flex items-start space-x-2">
              <div className="w-4 h-4 bg-amber-500 rounded-full flex-shrink-0 mt-0.5"></div>
              <div>
                <h5 className="text-sm font-medium text-amber-900">Strict Access Control</h5>
                <p className="text-xs text-amber-800 mt-1 leading-relaxed">
                  Each role has exclusive access to their designated interface. Cross-role access is automatically blocked with immediate redirection to the appropriate dashboard.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginModal;