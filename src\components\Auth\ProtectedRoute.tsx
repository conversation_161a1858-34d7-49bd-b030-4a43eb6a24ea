import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import LoadingSpinner from '../Common/LoadingSpinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
  requiredRole?: 'admin' | 'editor' | 'user';
  adminOnly?: boolean;
  editorOnly?: boolean;
  userOnly?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  requiredRole,
  adminOnly = false,
  editorOnly = false,
  userOnly = false
}) => {
  const { user, isLoading, hasPermission, isAdmin, isEditor, isUser, getRedirectPath } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size={32} />
          <p className="mt-4 text-gray-600">Verifying access...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  // STRICT ROLE-BASED ACCESS CONTROL
  
  // Admin-only access - Block editors and users
  if (adminOnly && !isAdmin()) {
    const redirectPath = getRedirectPath();
    return <Navigate to={redirectPath} replace />;
  }

  // Editor-only access - Block admins and users
  if (editorOnly && !isEditor()) {
    const redirectPath = getRedirectPath();
    return <Navigate to={redirectPath} replace />;
  }

  // User-only access - Block admins and editors
  if (userOnly && !isUser()) {
    const redirectPath = getRedirectPath();
    return <Navigate to={redirectPath} replace />;
  }

  // Check specific role requirement with strict enforcement
  if (requiredRole) {
    if (requiredRole === 'admin' && !isAdmin()) {
      return <Navigate to={getRedirectPath()} replace />;
    }
    if (requiredRole === 'editor' && !isEditor()) {
      return <Navigate to={getRedirectPath()} replace />;
    }
    if (requiredRole === 'user' && !isUser()) {
      return <Navigate to={getRedirectPath()} replace />;
    }
  }

  // Check specific permission requirement
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="w-16 h-16 bg-red-100 text-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600 mb-6">
            You don't have the required permission: <code className="bg-gray-100 px-2 py-1 rounded text-sm">{requiredPermission}</code>
          </p>
          <button
            onClick={() => window.location.href = getRedirectPath()}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go to Your Dashboard
          </button>
        </div>
      </div>
    );
  }

  // User has required permissions, render the protected content
  return <>{children}</>;
};

export default ProtectedRoute;