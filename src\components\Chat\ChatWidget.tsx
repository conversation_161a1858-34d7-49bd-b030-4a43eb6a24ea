import React, { useState, useEffect, useRef } from 'react';
import { MessageCircle, X, Send, Paperclip, Smile, Users, Phone, Video, MoreVertical, Search, Image, File, Mic, Settings } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import EmojiPicker from './EmojiPicker';
import FileUpload from './FileUpload';
import MessageList from './MessageList';
import UserList from './UserList';
import { useChatSocket } from '../../hooks/useChatSocket';
import { ChatMessage, ChatUser, ChatRoom } from '../../types/chat';

interface ChatWidgetProps {
  position?: 'bottom-right' | 'bottom-left';
}

const ChatWidget: React.FC<ChatWidgetProps> = ({ position = 'bottom-right' }) => {
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [message, setMessage] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [showUserList, setShowUserList] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeRoom, setActiveRoom] = useState<string>('general');
  const [isTyping, setIsTyping] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  
  const messageInputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Custom hook for WebSocket functionality
  const {
    messages,
    users,
    rooms,
    isConnected,
    sendMessage,
    joinRoom,
    leaveRoom,
    markAsRead,
    editMessage,
    deleteMessage,
    addReaction,
    removeReaction,
    typingUsers,
    startTyping,
    stopTyping
  } = useChatSocket(user?.id || '', activeRoom);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current && isOpen) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isOpen]);

  // Handle typing indicator
  useEffect(() => {
    if (message.trim() && !isTyping) {
      setIsTyping(true);
      startTyping();
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        stopTyping();
      }
    }, 1000);

    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [message, isTyping, startTyping, stopTyping]);

  // Update unread count
  useEffect(() => {
    if (!isOpen) {
      const unread = messages.filter(msg => !msg.read && msg.senderId !== user?.id).length;
      setUnreadCount(unread);
    } else {
      setUnreadCount(0);
      // Mark messages as read when chat is open
      messages.forEach(msg => {
        if (!msg.read && msg.senderId !== user?.id) {
          markAsRead(msg.id);
        }
      });
    }
  }, [messages, isOpen, user?.id, markAsRead]);

  // Request notification permission
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  // Show notifications for new messages when chat is closed
  useEffect(() => {
    if (!isOpen && messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.senderId !== user?.id && 'Notification' in window && Notification.permission === 'granted') {
        const notification = new Notification(`New message from ${lastMessage.senderName}`, {
          body: lastMessage.type === 'text' ? lastMessage.content : 'Sent an attachment',
          icon: '/shield-check.svg',
          tag: 'chat-message'
        });

        notification.onclick = () => {
          setIsOpen(true);
          notification.close();
        };

        setTimeout(() => notification.close(), 5000);
      }
    }
  }, [messages, isOpen, user?.id]);

  const handleSendMessage = async () => {
    if (!message.trim() || !user) return;

    const newMessage: Omit<ChatMessage, 'id' | 'timestamp'> = {
      content: message.trim(),
      senderId: user.id,
      senderName: user.name,
      senderAvatar: user.avatar,
      roomId: activeRoom,
      type: 'text',
      read: false,
      reactions: []
    };

    await sendMessage(newMessage);
    setMessage('');
    setShowEmojiPicker(false);
    
    if (messageInputRef.current) {
      messageInputRef.current.focus();
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    setMessage(prev => prev + emoji);
    setShowEmojiPicker(false);
    if (messageInputRef.current) {
      messageInputRef.current.focus();
    }
  };

  const handleFileUpload = async (files: File[]) => {
    if (!user) return;

    for (const file of files) {
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        alert(`File ${file.name} is too large. Maximum size is 10MB.`);
        continue;
      }

      const fileMessage: Omit<ChatMessage, 'id' | 'timestamp'> = {
        content: file.name,
        senderId: user.id,
        senderName: user.name,
        senderAvatar: user.avatar,
        roomId: activeRoom,
        type: file.type.startsWith('image/') ? 'image' : 'file',
        fileUrl: URL.createObjectURL(file), // In production, upload to server first
        fileSize: file.size,
        read: false,
        reactions: []
      };

      await sendMessage(fileMessage);
    }
    setShowFileUpload(false);
  };

  const filteredMessages = messages.filter(msg =>
    searchTerm === '' || 
    msg.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
    msg.senderName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const onlineUsers = users.filter(u => u.isOnline);
  const currentRoom = rooms.find(r => r.id === activeRoom);

  if (!user) {
    return null; // Don't show chat if user is not logged in
  }

  const positionClasses = position === 'bottom-right' 
    ? 'bottom-4 right-4' 
    : 'bottom-4 left-4';

  return (
    <>
      {/* Chat Bubble (when closed) */}
      {!isOpen && (
        <button
          onClick={() => setIsOpen(true)}
          className={`fixed ${positionClasses} z-50 bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 group`}
        >
          <MessageCircle size={24} />
          {unreadCount > 0 && (
            <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold animate-pulse">
              {unreadCount > 99 ? '99+' : unreadCount}
            </div>
          )}
          <div className="absolute -top-12 right-0 bg-gray-900 text-white px-3 py-1 rounded-lg text-sm opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
            {isConnected ? 'Chat with community' : 'Connecting...'}
          </div>
        </button>
      )}

      {/* Chat Window */}
      {isOpen && (
        <div className={`fixed ${positionClasses} z-50 bg-white rounded-2xl shadow-2xl border border-gray-200 transition-all duration-300 ${
          isMinimized ? 'h-16' : 'h-[600px]'
        } w-96 flex flex-col overflow-hidden`}>
          
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <Users size={16} />
                </div>
                <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                  isConnected ? 'bg-green-400' : 'bg-red-400'
                }`}></div>
              </div>
              <div>
                <h3 className="font-semibold text-sm">{currentRoom?.name || 'Community Chat'}</h3>
                <p className="text-xs text-blue-100">
                  {onlineUsers.length} online • {typingUsers.length > 0 && `${typingUsers.join(', ')} typing...`}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowUserList(!showUserList)}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                title="Show users"
              >
                <Users size={16} />
              </button>
              <button
                onClick={() => setIsMinimized(!isMinimized)}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                title={isMinimized ? 'Expand' : 'Minimize'}
              >
                {isMinimized ? '□' : '−'}
              </button>
              <button
                onClick={() => setIsOpen(false)}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                title="Close chat"
              >
                <X size={16} />
              </button>
            </div>
          </div>

          {!isMinimized && (
            <>
              {/* Search Bar */}
              <div className="p-3 border-b border-gray-100">
                <div className="relative">
                  <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search messages..."
                    className="w-full pl-9 pr-4 py-2 border border-gray-200 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  />
                </div>
              </div>

              {/* Messages Area */}
              <div className="flex-1 flex">
                <div className={`flex-1 flex flex-col ${showUserList ? 'border-r border-gray-200' : ''}`}>
                  <MessageList
                    messages={filteredMessages}
                    currentUserId={user.id}
                    onEditMessage={editMessage}
                    onDeleteMessage={deleteMessage}
                    onAddReaction={addReaction}
                    onRemoveReaction={removeReaction}
                  />
                  <div ref={messagesEndRef} />
                </div>

                {/* User List Sidebar */}
                {showUserList && (
                  <UserList
                    users={users}
                    currentUserId={user.id}
                    onClose={() => setShowUserList(false)}
                  />
                )}
              </div>

              {/* Typing Indicator */}
              {typingUsers.length > 0 && (
                <div className="px-4 py-2 text-xs text-gray-500 border-t border-gray-100">
                  <div className="flex items-center space-x-2">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                    <span>{typingUsers.join(', ')} {typingUsers.length === 1 ? 'is' : 'are'} typing...</span>
                  </div>
                </div>
              )}

              {/* Input Area */}
              <div className="p-4 border-t border-gray-100">
                <div className="flex items-end space-x-2">
                  <div className="flex-1 relative">
                    <input
                      ref={messageInputRef}
                      type="text"
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Type a message..."
                      className="w-full px-4 py-3 pr-20 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
                      disabled={!isConnected}
                    />
                    
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                      <button
                        onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                        className="p-1.5 text-gray-400 hover:text-gray-600 transition-colors"
                        title="Add emoji"
                      >
                        <Smile size={16} />
                      </button>
                      <button
                        onClick={() => setShowFileUpload(!showFileUpload)}
                        className="p-1.5 text-gray-400 hover:text-gray-600 transition-colors"
                        title="Attach file"
                      >
                        <Paperclip size={16} />
                      </button>
                    </div>
                  </div>
                  
                  <button
                    onClick={handleSendMessage}
                    disabled={!message.trim() || !isConnected}
                    className="bg-blue-500 text-white p-3 rounded-xl hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    title="Send message"
                  >
                    <Send size={16} />
                  </button>
                </div>

                {/* Connection Status */}
                {!isConnected && (
                  <div className="mt-2 text-xs text-red-500 flex items-center space-x-1">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span>Reconnecting...</span>
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      )}

      {/* Emoji Picker */}
      {showEmojiPicker && (
        <EmojiPicker
          onEmojiSelect={handleEmojiSelect}
          onClose={() => setShowEmojiPicker(false)}
          position={position}
        />
      )}

      {/* File Upload Modal */}
      {showFileUpload && (
        <FileUpload
          onFileUpload={handleFileUpload}
          onClose={() => setShowFileUpload(false)}
        />
      )}
    </>
  );
};

export default ChatWidget;