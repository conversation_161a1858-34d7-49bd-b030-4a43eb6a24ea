import React, { useState } from 'react';
import { MoreVertical, Edit, Trash2, Reply, Heart, ThumbsUp, Smile, Download, Eye } from 'lucide-react';
import { ChatMessage } from '../../types/chat';
import { format, isToday, isYesterday } from 'date-fns';

interface MessageListProps {
  messages: ChatMessage[];
  currentUserId: string;
  onEditMessage: (messageId: string, newContent: string) => void;
  onDeleteMessage: (messageId: string) => void;
  onAddReaction: (messageId: string, emoji: string) => void;
  onRemoveReaction: (messageId: string, emoji: string) => void;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  currentUserId,
  onEditMessage,
  onDeleteMessage,
  onAddReaction,
  onRemoveReaction
}) => {
  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');
  const [showMenuForMessage, setShowMenuForMessage] = useState<string | null>(null);

  const formatMessageTime = (timestamp: Date) => {
    if (isToday(timestamp)) {
      return format(timestamp, 'HH:mm');
    } else if (isYesterday(timestamp)) {
      return `Yesterday ${format(timestamp, 'HH:mm')}`;
    } else {
      return format(timestamp, 'MMM d, HH:mm');
    }
  };

  const handleEditStart = (message: ChatMessage) => {
    setEditingMessageId(message.id);
    setEditContent(message.content);
    setShowMenuForMessage(null);
  };

  const handleEditSave = () => {
    if (editingMessageId && editContent.trim()) {
      onEditMessage(editingMessageId, editContent.trim());
      setEditingMessageId(null);
      setEditContent('');
    }
  };

  const handleEditCancel = () => {
    setEditingMessageId(null);
    setEditContent('');
  };

  const handleReaction = (messageId: string, emoji: string) => {
    const message = messages.find(m => m.id === messageId);
    if (!message) return;

    const existingReaction = message.reactions.find(r => r.emoji === emoji && r.userId === currentUserId);
    
    if (existingReaction) {
      onRemoveReaction(messageId, emoji);
    } else {
      onAddReaction(messageId, emoji);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const groupedMessages = messages.reduce((groups: ChatMessage[][], message, index) => {
    const prevMessage = messages[index - 1];
    const shouldGroup = prevMessage && 
      prevMessage.senderId === message.senderId && 
      (message.timestamp.getTime() - prevMessage.timestamp.getTime()) < 5 * 60 * 1000; // 5 minutes

    if (shouldGroup && groups.length > 0) {
      groups[groups.length - 1].push(message);
    } else {
      groups.push([message]);
    }
    return groups;
  }, []);

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4">
      {groupedMessages.map((group, groupIndex) => (
        <div key={groupIndex} className="space-y-1">
          {group.map((message, messageIndex) => {
            const isOwn = message.senderId === currentUserId;
            const isFirstInGroup = messageIndex === 0;
            
            return (
              <div
                key={message.id}
                className={`flex ${isOwn ? 'justify-end' : 'justify-start'} group`}
              >
                <div className={`max-w-xs lg:max-w-md ${isOwn ? 'order-2' : 'order-1'}`}>
                  {isFirstInGroup && (
                    <div className={`flex items-center space-x-2 mb-1 ${isOwn ? 'justify-end' : 'justify-start'}`}>
                      {!isOwn && (
                        <img
                          src={message.senderAvatar || 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=40&h=40&dpr=1'}
                          alt={message.senderName}
                          className="w-6 h-6 rounded-full object-cover"
                        />
                      )}
                      <span className="text-xs text-gray-500 font-medium">
                        {isOwn ? 'You' : message.senderName}
                      </span>
                      <span className="text-xs text-gray-400">
                        {formatMessageTime(message.timestamp)}
                      </span>
                    </div>
                  )}
                  
                  <div className="relative">
                    <div
                      className={`rounded-2xl px-4 py-2 ${
                        isOwn
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-900'
                      } ${message.edited ? 'border-l-2 border-yellow-400' : ''}`}
                    >
                      {editingMessageId === message.id ? (
                        <div className="space-y-2">
                          <input
                            type="text"
                            value={editContent}
                            onChange={(e) => setEditContent(e.target.value)}
                            className="w-full bg-transparent border-b border-current focus:outline-none"
                            onKeyPress={(e) => {
                              if (e.key === 'Enter') handleEditSave();
                              if (e.key === 'Escape') handleEditCancel();
                            }}
                            autoFocus
                          />
                          <div className="flex space-x-2">
                            <button
                              onClick={handleEditSave}
                              className="text-xs px-2 py-1 bg-green-500 text-white rounded"
                            >
                              Save
                            </button>
                            <button
                              onClick={handleEditCancel}
                              className="text-xs px-2 py-1 bg-gray-500 text-white rounded"
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      ) : (
                        <>
                          {message.type === 'text' && (
                            <p className="break-words">{message.content}</p>
                          )}
                          
                          {message.type === 'image' && (
                            <div className="space-y-2">
                              <img
                                src={message.fileUrl}
                                alt={message.content}
                                className="max-w-full h-auto rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                                onClick={() => window.open(message.fileUrl, '_blank')}
                              />
                              <p className="text-sm opacity-75">{message.content}</p>
                            </div>
                          )}
                          
                          {message.type === 'file' && (
                            <div className="flex items-center space-x-3 p-2 bg-white/10 rounded-lg">
                              <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
                                <Download size={16} />
                              </div>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">{message.content}</p>
                                <p className="text-xs opacity-75">
                                  {message.fileSize && formatFileSize(message.fileSize)}
                                </p>
                              </div>
                              <button
                                onClick={() => window.open(message.fileUrl, '_blank')}
                                className="p-1 hover:bg-white/10 rounded"
                              >
                                <Download size={14} />
                              </button>
                            </div>
                          )}
                          
                          {message.edited && (
                            <p className="text-xs opacity-60 mt-1">(edited)</p>
                          )}
                        </>
                      )}
                    </div>

                    {/* Message Actions */}
                    <div className={`absolute top-0 ${isOwn ? 'left-0 -translate-x-full' : 'right-0 translate-x-full'} opacity-0 group-hover:opacity-100 transition-opacity`}>
                      <div className="flex items-center space-x-1 bg-white shadow-lg rounded-lg p-1 border border-gray-200">
                        <button
                          onClick={() => handleReaction(message.id, '❤️')}
                          className="p-1 hover:bg-gray-100 rounded text-sm"
                          title="React with heart"
                        >
                          ❤️
                        </button>
                        <button
                          onClick={() => handleReaction(message.id, '👍')}
                          className="p-1 hover:bg-gray-100 rounded text-sm"
                          title="React with thumbs up"
                        >
                          👍
                        </button>
                        <button
                          onClick={() => handleReaction(message.id, '😊')}
                          className="p-1 hover:bg-gray-100 rounded text-sm"
                          title="React with smile"
                        >
                          😊
                        </button>
                        {isOwn && (
                          <button
                            onClick={() => setShowMenuForMessage(showMenuForMessage === message.id ? null : message.id)}
                            className="p-1 hover:bg-gray-100 rounded"
                            title="More options"
                          >
                            <MoreVertical size={14} />
                          </button>
                        )}
                      </div>
                    </div>

                    {/* Message Menu */}
                    {showMenuForMessage === message.id && isOwn && (
                      <div className={`absolute top-8 ${isOwn ? 'left-0 -translate-x-full' : 'right-0 translate-x-full'} bg-white shadow-lg rounded-lg border border-gray-200 py-1 z-10`}>
                        <button
                          onClick={() => handleEditStart(message)}
                          className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 flex items-center space-x-2"
                        >
                          <Edit size={14} />
                          <span>Edit</span>
                        </button>
                        <button
                          onClick={() => {
                            onDeleteMessage(message.id);
                            setShowMenuForMessage(null);
                          }}
                          className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 text-red-600 flex items-center space-x-2"
                        >
                          <Trash2 size={14} />
                          <span>Delete</span>
                        </button>
                      </div>
                    )}

                    {/* Reactions */}
                    {message.reactions.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-1">
                        {Object.entries(
                          message.reactions.reduce((acc, reaction) => {
                            acc[reaction.emoji] = (acc[reaction.emoji] || 0) + 1;
                            return acc;
                          }, {} as Record<string, number>)
                        ).map(([emoji, count]) => (
                          <button
                            key={emoji}
                            onClick={() => handleReaction(message.id, emoji)}
                            className={`text-xs px-2 py-1 rounded-full border transition-colors ${
                              message.reactions.some(r => r.emoji === emoji && r.userId === currentUserId)
                                ? 'bg-blue-100 border-blue-300 text-blue-700'
                                : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                            }`}
                          >
                            {emoji} {count}
                          </button>
                        ))}
                      </div>
                    )}

                    {/* Read Receipt */}
                    {isOwn && message.read && (
                      <div className="flex justify-end mt-1">
                        <div className="text-xs text-gray-400 flex items-center space-x-1">
                          <Eye size={10} />
                          <span>Read</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ))}
      
      {messages.length === 0 && (
        <div className="text-center text-gray-500 py-8">
          <MessageCircle size={48} className="mx-auto mb-4 opacity-50" />
          <p>No messages yet. Start the conversation!</p>
        </div>
      )}
    </div>
  );
};

export default MessageList;