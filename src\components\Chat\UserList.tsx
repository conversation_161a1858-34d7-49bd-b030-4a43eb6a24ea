import React from 'react';
import { X, Crown, Shield, User } from 'lucide-react';
import { ChatUser } from '../../types/chat';

interface UserListProps {
  users: ChatUser[];
  currentUserId: string;
  onClose: () => void;
}

const UserList: React.FC<UserListProps> = ({ users, currentUserId, onClose }) => {
  const sortedUsers = [...users].sort((a, b) => {
    // Sort by online status first, then by role, then by name
    if (a.isOnline !== b.isOnline) {
      return a.isOnline ? -1 : 1;
    }
    if (a.role !== b.role) {
      const roleOrder = { admin: 0, moderator: 1, user: 2 };
      return roleOrder[a.role] - roleOrder[b.role];
    }
    return a.name.localeCompare(b.name);
  });

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Crown size={12} className="text-yellow-500" />;
      case 'moderator':
        return <Shield size={12} className="text-blue-500" />;
      default:
        return <User size={12} className="text-gray-400" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'moderator':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const onlineUsers = sortedUsers.filter(user => user.isOnline);
  const offlineUsers = sortedUsers.filter(user => !user.isOnline);

  return (
    <div className="w-64 bg-gray-50 border-l border-gray-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 flex items-center justify-between">
        <h3 className="font-semibold text-gray-900">
          Users ({users.length})
        </h3>
        <button
          onClick={onClose}
          className="p-1 hover:bg-gray-200 rounded transition-colors"
        >
          <X size={16} />
        </button>
      </div>

      {/* User List */}
      <div className="flex-1 overflow-y-auto">
        {/* Online Users */}
        {onlineUsers.length > 0 && (
          <div className="p-3">
            <h4 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
              Online ({onlineUsers.length})
            </h4>
            <div className="space-y-2">
              {onlineUsers.map((user) => (
                <div
                  key={user.id}
                  className={`flex items-center space-x-3 p-2 rounded-lg hover:bg-white transition-colors ${
                    user.id === currentUserId ? 'bg-blue-50 border border-blue-200' : ''
                  }`}
                >
                  <div className="relative">
                    <img
                      src={user.avatar || 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=32&h=32&dpr=1'}
                      alt={user.name}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-1">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {user.name}
                        {user.id === currentUserId && (
                          <span className="text-xs text-gray-500 ml-1">(You)</span>
                        )}
                      </p>
                      {getRoleIcon(user.role)}
                    </div>
                    {user.role !== 'user' && (
                      <span className={`inline-block px-2 py-0.5 text-xs font-medium rounded-full border ${getRoleColor(user.role)}`}>
                        {user.role}
                      </span>
                    )}
                    {user.isTyping && (
                      <p className="text-xs text-blue-500 italic">typing...</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Offline Users */}
        {offlineUsers.length > 0 && (
          <div className="p-3 border-t border-gray-200">
            <h4 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
              Offline ({offlineUsers.length})
            </h4>
            <div className="space-y-2">
              {offlineUsers.map((user) => (
                <div
                  key={user.id}
                  className={`flex items-center space-x-3 p-2 rounded-lg opacity-60 ${
                    user.id === currentUserId ? 'bg-blue-50 border border-blue-200' : ''
                  }`}
                >
                  <div className="relative">
                    <img
                      src={user.avatar || 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=32&h=32&dpr=1'}
                      alt={user.name}
                      className="w-8 h-8 rounded-full object-cover grayscale"
                    />
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-gray-400 border-2 border-white rounded-full"></div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-1">
                      <p className="text-sm font-medium text-gray-600 truncate">
                        {user.name}
                        {user.id === currentUserId && (
                          <span className="text-xs text-gray-400 ml-1">(You)</span>
                        )}
                      </p>
                      {getRoleIcon(user.role)}
                    </div>
                    {user.role !== 'user' && (
                      <span className={`inline-block px-2 py-0.5 text-xs font-medium rounded-full border opacity-75 ${getRoleColor(user.role)}`}>
                        {user.role}
                      </span>
                    )}
                    {user.lastSeen && (
                      <p className="text-xs text-gray-400">
                        Last seen {new Date(user.lastSeen).toLocaleDateString()}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-gray-200 bg-white">
        <div className="text-xs text-gray-500 text-center">
          <p>Community Guidelines</p>
          <p className="mt-1">Be respectful and helpful</p>
        </div>
      </div>
    </div>
  );
};

export default UserList;