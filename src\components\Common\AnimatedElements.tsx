import React, { useState, useEffect } from 'react';
import { Eye, EyeOff, ChevronDown, Settings, Bell, User, Search, Plus, Heart, Star, Share2, Download, Filter, Calendar, MapPin } from 'lucide-react';
import AnimatedToggle, { AnimatedFilterDropdown } from './AnimatedToggle';

const AnimatedElementsDemo: React.FC = () => {
  const [showDemo, setShowDemo] = useState(false);
  const [assistanceOptions, setAssistanceOptions] = useState([
    { id: '1', label: 'Emergency Shelter', checked: false },
    { id: '2', label: 'Food & Water', checked: false },
    { id: '3', label: 'Medical Aid', checked: true },
    { id: '4', label: 'Evacuation Support', checked: false },
    { id: '5', label: 'Pet Care', checked: false },
    { id: '6', label: 'Transportation', checked: false },
    { id: '7', label: 'Cleanup Volunteers', checked: false },
    { id: '8', label: 'Construction Help', checked: false },
    { id: '9', label: 'Financial Support', checked: false }
  ]);

  const [notificationSettings, setNotificationSettings] = useState([
    { id: '1', label: 'Emergency Alerts', checked: true },
    { id: '2', label: 'Community Updates', checked: true },
    { id: '3', label: 'Assistance Requests', checked: false },
    { id: '4', label: 'Weekly Digest', checked: true }
  ]);

  const handleAssistanceChange = (optionId: string, checked: boolean) => {
    setAssistanceOptions(prev =>
      prev.map(option =>
        option.id === optionId ? { ...option, checked } : option
      )
    );
  };

  const handleNotificationChange = (optionId: string, checked: boolean) => {
    setNotificationSettings(prev =>
      prev.map(option =>
        option.id === optionId ? { ...option, checked } : option
      )
    );
  };

  const selectedAssistanceCount = assistanceOptions.filter(opt => opt.checked).length;
  const selectedNotificationCount = notificationSettings.filter(opt => opt.checked).length;

  return (
    <div className="space-y-8 p-8 bg-gray-50 min-h-screen">
      {/* Demo Toggle Button */}
      <div className="text-center">
        <button
          onClick={() => setShowDemo(!showDemo)}
          className={`px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 ${
            showDemo
              ? 'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg'
              : 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg'
          }`}
        >
          {showDemo ? (
            <span className="flex items-center space-x-2">
              <EyeOff size={20} />
              <span>Hide Demo Elements</span>
            </span>
          ) : (
            <span className="flex items-center space-x-2">
              <Eye size={20} />
              <span>Show Animated Elements</span>
            </span>
          )}
        </button>
      </div>

      {/* Animated Demo Content */}
      <div
        className={`transition-all duration-700 ease-out transform ${
          showDemo
            ? 'opacity-100 translate-y-0 scale-100'
            : 'opacity-0 -translate-y-8 scale-95 pointer-events-none'
        }`}
      >
        <div className="max-w-6xl mx-auto space-y-12">
          {/* Header */}
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Animated Toggle Elements
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Smooth, interactive elements that enhance user experience with beautiful animations
            </p>
          </div>

          {/* Filter Dropdowns Section */}
          <section className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">Filter Dropdowns</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <AnimatedFilterDropdown
                title="Assistance Needed"
                options={assistanceOptions}
                onOptionChange={handleAssistanceChange}
                selectedCount={selectedAssistanceCount}
              />
              
              <AnimatedFilterDropdown
                title="Notification Settings"
                options={notificationSettings}
                onOptionChange={handleNotificationChange}
                selectedCount={selectedNotificationCount}
              />

              <AnimatedToggle
                trigger={
                  <button className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-left flex items-center justify-between transition-all duration-200 hover:border-gray-300 bg-white">
                    <span className="text-gray-700 flex items-center space-x-2">
                      <Calendar size={16} className="text-gray-400" />
                      <span>Date Range</span>
                    </span>
                    <ChevronDown size={16} className="text-gray-400" />
                  </button>
                }
                animationType="slide"
                className="w-full"
              >
                <div className="p-6 w-80">
                  <h3 className="font-semibold text-gray-900 mb-4">Select Date Range</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">From</label>
                      <input
                        type="date"
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">To</label>
                      <input
                        type="date"
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div className="flex space-x-2 pt-2">
                      <button className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Apply
                      </button>
                      <button className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Clear
                      </button>
                    </div>
                  </div>
                </div>
              </AnimatedToggle>
            </div>
          </section>

          {/* Action Buttons Section */}
          <section className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">Action Buttons</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <AnimatedToggle
                trigger={
                  <button className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-sm">
                    <Plus size={16} />
                    <span>Quick Actions</span>
                  </button>
                }
                animationType="scale"
                position="bottom"
              >
                <div className="p-4 w-64">
                  <div className="space-y-2">
                    <button className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 rounded-lg transition-colors">
                      <Heart size={16} className="text-red-500" />
                      <span>Add to Favorites</span>
                    </button>
                    <button className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 rounded-lg transition-colors">
                      <Share2 size={16} className="text-blue-500" />
                      <span>Share Report</span>
                    </button>
                    <button className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 rounded-lg transition-colors">
                      <Download size={16} className="text-green-500" />
                      <span>Download Data</span>
                    </button>
                    <button className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 rounded-lg transition-colors">
                      <Bell size={16} className="text-orange-500" />
                      <span>Set Alert</span>
                    </button>
                  </div>
                </div>
              </AnimatedToggle>

              <AnimatedToggle
                trigger={
                  <button className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-sm">
                    <User size={16} />
                    <span>Profile Menu</span>
                  </button>
                }
                animationType="fade"
                position="bottom"
              >
                <div className="p-4 w-56">
                  <div className="space-y-1">
                    <div className="px-4 py-3 border-b border-gray-100">
                      <p className="font-medium text-gray-900">John Anderson</p>
                      <p className="text-sm text-gray-600"><EMAIL></p>
                    </div>
                    <button className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 rounded-lg transition-colors">
                      <User size={16} className="text-gray-400" />
                      <span>View Profile</span>
                    </button>
                    <button className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 rounded-lg transition-colors">
                      <Settings size={16} className="text-gray-400" />
                      <span>Settings</span>
                    </button>
                    <button className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 rounded-lg transition-colors">
                      <Bell size={16} className="text-gray-400" />
                      <span>Notifications</span>
                    </button>
                    <div className="border-t border-gray-100 pt-1">
                      <button className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-red-50 rounded-lg transition-colors text-red-600">
                        <span>Sign Out</span>
                      </button>
                    </div>
                  </div>
                </div>
              </AnimatedToggle>

              <AnimatedToggle
                trigger={
                  <button className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl hover:from-orange-600 hover:to-orange-700 transition-all duration-200 shadow-sm">
                    <Search size={16} />
                    <span>Advanced Search</span>
                  </button>
                }
                animationType="bounce"
                position="bottom"
              >
                <div className="p-6 w-96">
                  <h3 className="font-semibold text-gray-900 mb-4">Advanced Search</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Keywords</label>
                      <input
                        type="text"
                        placeholder="Search reports, locations..."
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                          <option>All Statuses</option>
                          <option>Verified</option>
                          <option>Pending</option>
                          <option>Rejected</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
                        <select className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                          <option>All Types</option>
                          <option>Flood</option>
                          <option>Fire</option>
                          <option>Earthquake</option>
                        </select>
                      </div>
                    </div>
                    <div className="flex space-x-2 pt-2">
                      <button className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Search
                      </button>
                      <button className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Reset
                      </button>
                    </div>
                  </div>
                </div>
              </AnimatedToggle>

              <AnimatedToggle
                trigger={
                  <button className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-indigo-500 to-indigo-600 text-white rounded-xl hover:from-indigo-600 hover:to-indigo-700 transition-all duration-200 shadow-sm">
                    <MapPin size={16} />
                    <span>Location Filter</span>
                  </button>
                }
                animationType="slide"
                position="bottom"
              >
                <div className="p-6 w-80">
                  <h3 className="font-semibold text-gray-900 mb-4">Filter by Location</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Search Location</label>
                      <input
                        type="text"
                        placeholder="City, state, or zip code"
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Radius</label>
                      <select className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option>5 miles</option>
                        <option>10 miles</option>
                        <option>25 miles</option>
                        <option>50 miles</option>
                        <option>100 miles</option>
                      </select>
                    </div>
                    <div className="flex items-center">
                      <input type="checkbox" id="currentLocation" className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                      <label htmlFor="currentLocation" className="text-sm text-gray-700">
                        Use my current location
                      </label>
                    </div>
                    <div className="flex space-x-2 pt-2">
                      <button className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        Apply
                      </button>
                      <button className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        Clear
                      </button>
                    </div>
                  </div>
                </div>
              </AnimatedToggle>
            </div>
          </section>

          {/* Animation Types Demo */}
          <section className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">Animation Types</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {['slide', 'fade', 'scale', 'bounce'].map((animationType) => (
                <AnimatedToggle
                  key={animationType}
                  trigger={
                    <button className="w-full px-4 py-3 border-2 border-dashed border-gray-300 rounded-xl hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 text-gray-600 hover:text-blue-600">
                      <div className="text-center">
                        <div className="text-lg font-medium capitalize">{animationType}</div>
                        <div className="text-sm">Animation</div>
                      </div>
                    </button>
                  }
                  animationType={animationType as any}
                  position="bottom"
                >
                  <div className="p-6 w-64 text-center">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                      <Star size={24} className="text-white" />
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2 capitalize">{animationType} Animation</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      This content appears with a smooth {animationType} animation effect.
                    </p>
                    <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                      Action Button
                    </button>
                  </div>
                </AnimatedToggle>
              ))}
            </div>
          </section>

          {/* Usage Instructions */}
          <section className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-8">
            <h2 className="text-2xl font-semibold text-blue-900 mb-6">How to Use</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="font-semibold text-blue-800 mb-3">Basic Toggle</h3>
                <div className="bg-white rounded-lg p-4 border border-blue-200">
                  <pre className="text-sm text-gray-700 overflow-x-auto">
{`<AnimatedToggle
  trigger={<button>Click me</button>}
  animationType="slide"
  position="bottom"
>
  <div>Hidden content</div>
</AnimatedToggle>`}
                  </pre>
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-blue-800 mb-3">Filter Dropdown</h3>
                <div className="bg-white rounded-lg p-4 border border-blue-200">
                  <pre className="text-sm text-gray-700 overflow-x-auto">
{`<AnimatedFilterDropdown
  title="Filter Options"
  options={filterOptions}
  onOptionChange={handleChange}
  selectedCount={selectedCount}
/>`}
                  </pre>
                </div>
              </div>
            </div>
            <div className="mt-6 p-4 bg-blue-100 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Features:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Multiple animation types: slide, fade, scale, bounce</li>
                <li>• Configurable positioning: top, bottom, left, right</li>
                <li>• Click outside to close functionality</li>
                <li>• Smooth transitions with customizable duration</li>
                <li>• Built-in search and filtering for dropdowns</li>
                <li>• Accessible keyboard navigation</li>
              </ul>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};

export default AnimatedElementsDemo;