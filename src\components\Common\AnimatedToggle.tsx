import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, ChevronUp, Filter, X, Check } from 'lucide-react';

interface AnimatedToggleProps {
  trigger?: React.ReactNode;
  children?: React.ReactNode;
  className?: string;
  position?: 'bottom' | 'top' | 'left' | 'right';
  animationType?: 'slide' | 'fade' | 'scale' | 'bounce';
  duration?: number;
  closeOnClickOutside?: boolean;
  closeOnSelect?: boolean;
}

const AnimatedToggle: React.FC<AnimatedToggleProps> = ({
  trigger,
  children,
  className = '',
  position = 'bottom',
  animationType = 'slide',
  duration = 300,
  closeOnClickOutside = true,
  closeOnSelect = false
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close
  useEffect(() => {
    if (!closeOnClickOutside) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsVisible(false);
      }
    };

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isVisible, closeOnClickOutside]);

  const handleToggle = () => {
    if (isAnimating) return;
    
    setIsAnimating(true);
    setIsVisible(!isVisible);
    
    setTimeout(() => {
      setIsAnimating(false);
    }, duration);
  };

  const getAnimationClasses = () => {
    const baseClasses = 'transition-all ease-out';
    const durationClass = `duration-${duration}`;
    
    switch (animationType) {
      case 'slide':
        return `${baseClasses} ${durationClass} ${
          isVisible 
            ? 'opacity-100 translate-y-0 scale-100' 
            : 'opacity-0 -translate-y-2 scale-95'
        }`;
      case 'fade':
        return `${baseClasses} ${durationClass} ${
          isVisible ? 'opacity-100' : 'opacity-0'
        }`;
      case 'scale':
        return `${baseClasses} ${durationClass} ${
          isVisible 
            ? 'opacity-100 scale-100' 
            : 'opacity-0 scale-95'
        }`;
      case 'bounce':
        return `${baseClasses} ${durationClass} ${
          isVisible 
            ? 'opacity-100 translate-y-0 scale-100 animate-bounce-in' 
            : 'opacity-0 translate-y-4 scale-90'
        }`;
      default:
        return `${baseClasses} ${durationClass}`;
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'top':
        return 'bottom-full mb-2';
      case 'left':
        return 'right-full mr-2 top-0';
      case 'right':
        return 'left-full ml-2 top-0';
      case 'bottom':
      default:
        return 'top-full mt-2';
    }
  };

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Trigger Element */}
      <div onClick={handleToggle} className="cursor-pointer">
        {trigger || (
          <button className="flex items-center space-x-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <Filter size={16} />
            <span>Toggle</span>
            {isVisible ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          </button>
        )}
      </div>

      {/* Animated Content */}
      {(isVisible || isAnimating) && (
        <div
          ref={contentRef}
          className={`absolute z-50 ${getPositionClasses()} ${getAnimationClasses()}`}
          style={{
            pointerEvents: isVisible ? 'auto' : 'none'
          }}
        >
          <div 
            className="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden"
            onClick={closeOnSelect ? handleToggle : undefined}
          >
            {children || (
              <div className="p-4">
                <p className="text-gray-700">Default content</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Specialized Filter Dropdown Component
interface FilterOption {
  id: string;
  label: string;
  checked: boolean;
}

interface AnimatedFilterDropdownProps {
  title: string;
  options: FilterOption[];
  onOptionChange: (optionId: string, checked: boolean) => void;
  selectedCount?: number;
  className?: string;
}

export const AnimatedFilterDropdown: React.FC<AnimatedFilterDropdownProps> = ({
  title,
  options,
  onOptionChange,
  selectedCount = 0,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);

  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleClearAll = () => {
    options.forEach(option => {
      if (option.checked) {
        onOptionChange(option.id, false);
      }
    });
  };

  const handleSelectAll = () => {
    options.forEach(option => {
      if (!option.checked) {
        onOptionChange(option.id, true);
      }
    });
  };

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Trigger Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-left flex items-center justify-between transition-all duration-200 hover:border-gray-300 bg-white"
      >
        <span className="text-gray-700 flex items-center space-x-2">
          <Filter size={16} className="text-gray-400" />
          <span>
            {selectedCount === 0 
              ? title
              : `${title} (${selectedCount} selected)`
            }
          </span>
        </span>
        <div className="flex items-center space-x-2">
          {selectedCount > 0 && (
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
              {selectedCount}
            </span>
          )}
          <div className={`transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}>
            <ChevronDown size={16} className="text-gray-400" />
          </div>
        </div>
      </button>

      {/* Animated Dropdown */}
      <div
        className={`absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg z-50 overflow-hidden transition-all duration-300 ease-out transform origin-top ${
          isOpen
            ? 'opacity-100 scale-y-100 translate-y-0'
            : 'opacity-0 scale-y-95 -translate-y-2 pointer-events-none'
        }`}
      >
        {/* Header with Search */}
        <div className="p-4 border-b border-gray-100 bg-gray-50">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium text-gray-900">{title}</h3>
            <button
              onClick={() => setIsOpen(false)}
              className="p-1 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <X size={16} className="text-gray-400" />
            </button>
          </div>
          
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search options..."
            className="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
          />
          
          <div className="flex items-center space-x-2 mt-3">
            <button
              onClick={handleSelectAll}
              className="text-xs text-blue-600 hover:text-blue-700 font-medium"
            >
              Select All
            </button>
            <span className="text-gray-300">|</span>
            <button
              onClick={handleClearAll}
              className="text-xs text-gray-600 hover:text-gray-700 font-medium"
            >
              Clear All
            </button>
          </div>
        </div>

        {/* Options List */}
        <div className="max-h-64 overflow-y-auto">
          {filteredOptions.length > 0 ? (
            <div className="p-2">
              {filteredOptions.map((option, index) => (
                <label
                  key={option.id}
                  className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-all duration-150 hover:bg-blue-50 group ${
                    option.checked ? 'bg-blue-50' : ''
                  }`}
                  style={{
                    animationDelay: `${index * 50}ms`
                  }}
                >
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={option.checked}
                      onChange={(e) => onOptionChange(option.id, e.target.checked)}
                      className="sr-only"
                    />
                    <div
                      className={`w-5 h-5 border-2 rounded transition-all duration-200 flex items-center justify-center ${
                        option.checked
                          ? 'bg-blue-600 border-blue-600'
                          : 'border-gray-300 group-hover:border-blue-400'
                      }`}
                    >
                      {option.checked && (
                        <Check size={12} className="text-white animate-scale-in" />
                      )}
                    </div>
                  </div>
                  <span
                    className={`text-sm transition-colors ${
                      option.checked ? 'text-blue-900 font-medium' : 'text-gray-700'
                    }`}
                  >
                    {option.label}
                  </span>
                </label>
              ))}
            </div>
          ) : (
            <div className="p-8 text-center">
              <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Filter size={20} className="text-gray-400" />
              </div>
              <p className="text-gray-500 text-sm">No options found</p>
              <p className="text-gray-400 text-xs mt-1">Try adjusting your search</p>
            </div>
          )}
        </div>

        {/* Footer */}
        {selectedCount > 0 && (
          <div className="p-4 border-t border-gray-100 bg-gray-50">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">
                {selectedCount} option{selectedCount !== 1 ? 's' : ''} selected
              </span>
              <button
                onClick={() => setIsOpen(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
              >
                Apply Filters
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AnimatedToggle;