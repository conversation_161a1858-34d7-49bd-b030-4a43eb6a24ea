import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Ring, <PERSON>Off, Dot } from 'lucide-react';

interface NotificationIconProps {
  count?: number;
  hasUnread?: boolean;
  isActive?: boolean;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'minimal' | 'filled' | 'outline';
  showPulse?: boolean;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
  maxCount?: number;
}

const NotificationIcon: React.FC<NotificationIconProps> = ({
  count = 0,
  hasUnread = false,
  isActive = false,
  size = 'md',
  variant = 'default',
  showPulse = true,
  onClick,
  className = '',
  disabled = false,
  maxCount = 99
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [prevCount, setPrevCount] = useState(count);

  // Trigger animation when count changes
  useEffect(() => {
    if (count > prevCount && count > 0) {
      setIsAnimating(true);
      const timer = setTimeout(() => setIsAnimating(false), 600);
      return () => clearTimeout(timer);
    }
    setPrevCount(count);
  }, [count, prevCount]);

  // Size configurations
  const sizeConfig = {
    sm: {
      icon: 16,
      container: 'w-8 h-8',
      badge: 'w-4 h-4 text-xs',
      badgeOffset: '-top-1 -right-1',
      dot: 'w-2 h-2 top-0 right-0'
    },
    md: {
      icon: 20,
      container: 'w-10 h-10',
      badge: 'w-5 h-5 text-xs',
      badgeOffset: '-top-1.5 -right-1.5',
      dot: 'w-2.5 h-2.5 top-0.5 right-0.5'
    },
    lg: {
      icon: 24,
      container: 'w-12 h-12',
      badge: 'w-6 h-6 text-sm',
      badgeOffset: '-top-2 -right-2',
      dot: 'w-3 h-3 top-0.5 right-0.5'
    },
    xl: {
      icon: 28,
      container: 'w-14 h-14',
      badge: 'w-7 h-7 text-sm',
      badgeOffset: '-top-2.5 -right-2.5',
      dot: 'w-3.5 h-3.5 top-1 right-1'
    }
  };

  // Variant configurations
  const variantConfig = {
    default: {
      container: 'bg-gray-100 hover:bg-gray-200 text-gray-700',
      containerActive: 'bg-blue-100 text-blue-700',
      containerDisabled: 'bg-gray-50 text-gray-400 cursor-not-allowed'
    },
    minimal: {
      container: 'bg-transparent hover:bg-gray-100 text-gray-600',
      containerActive: 'bg-blue-50 text-blue-600',
      containerDisabled: 'bg-transparent text-gray-300 cursor-not-allowed'
    },
    filled: {
      container: 'bg-blue-500 hover:bg-blue-600 text-white',
      containerActive: 'bg-blue-600 text-white',
      containerDisabled: 'bg-gray-300 text-gray-500 cursor-not-allowed'
    },
    outline: {
      container: 'border-2 border-gray-300 hover:border-gray-400 bg-white text-gray-700',
      containerActive: 'border-blue-500 bg-blue-50 text-blue-700',
      containerDisabled: 'border-gray-200 bg-gray-50 text-gray-400 cursor-not-allowed'
    }
  };

  const config = sizeConfig[size];
  const variantStyles = variantConfig[variant];

  // Determine which icon to show
  const getIcon = () => {
    if (disabled) return BellOff;
    if (hasUnread || count > 0) return BellRing;
    return Bell;
  };

  const IconComponent = getIcon();

  // Format count display
  const displayCount = count > maxCount ? `${maxCount}+` : count.toString();

  // Container classes
  const containerClasses = `
    relative inline-flex items-center justify-center rounded-full transition-all duration-200 transform
    ${config.container}
    ${disabled 
      ? variantStyles.containerDisabled 
      : isActive 
        ? variantStyles.containerActive 
        : variantStyles.container
    }
    ${!disabled ? 'hover:scale-105 active:scale-95' : ''}
    ${isAnimating ? 'animate-pulse' : ''}
    ${className}
  `.trim();

  // Badge classes
  const badgeClasses = `
    absolute ${config.badgeOffset} ${config.badge}
    bg-red-500 text-white rounded-full flex items-center justify-center font-bold
    border-2 border-white shadow-sm
    ${isAnimating ? 'animate-bounce' : ''}
    ${showPulse && hasUnread ? 'animate-pulse' : ''}
  `.trim();

  // Dot classes
  const dotClasses = `
    absolute ${config.dot}
    bg-red-500 rounded-full border-2 border-white
    ${showPulse ? 'animate-pulse' : ''}
  `.trim();

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={containerClasses}
      aria-label={
        count > 0 
          ? `${count} unread notifications` 
          : hasUnread 
            ? 'Unread notifications' 
            : 'Notifications'
      }
      title={
        count > 0 
          ? `${count} unread notifications` 
          : hasUnread 
            ? 'You have unread notifications' 
            : 'Notifications'
      }
    >
      {/* Main Icon */}
      <IconComponent 
        size={config.icon} 
        className={`transition-all duration-200 ${
          hasUnread || count > 0 ? 'animate-pulse' : ''
        }`}
      />

      {/* Notification Badge (for count > 0) */}
      {count > 0 && (
        <span className={badgeClasses}>
          {displayCount}
        </span>
      )}

      {/* Notification Dot (for hasUnread without count) */}
      {hasUnread && count === 0 && (
        <span className={dotClasses} />
      )}

      {/* Pulse Ring Effect */}
      {showPulse && (hasUnread || count > 0) && !disabled && (
        <span className="absolute inset-0 rounded-full border-2 border-red-400 animate-ping opacity-20" />
      )}
    </button>
  );
};

// Demo component to showcase different states
export const NotificationIconDemo: React.FC = () => {
  const [demoCount, setDemoCount] = useState(0);
  const [hasUnread, setHasUnread] = useState(false);

  const incrementCount = () => setDemoCount(prev => prev + 1);
  const resetCount = () => setDemoCount(0);
  const toggleUnread = () => setHasUnread(prev => !prev);

  return (
    <div className="space-y-12 p-8 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Notification Icon System
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Professional notification icons with multiple states, sizes, and variants
        </p>
      </div>

      {/* Interactive Demo */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-6">Interactive Demo</h2>
        
        <div className="flex flex-col items-center space-y-6">
          <div className="flex items-center space-x-8">
            <NotificationIcon 
              count={demoCount} 
              hasUnread={hasUnread}
              size="xl"
              showPulse={true}
            />
          </div>
          
          <div className="flex flex-wrap gap-4 justify-center">
            <button
              onClick={incrementCount}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Add Notification (+1)
            </button>
            <button
              onClick={resetCount}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              Clear Count
            </button>
            <button
              onClick={toggleUnread}
              className="px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
            >
              Toggle Unread Dot
            </button>
          </div>
          
          <div className="text-center text-sm text-gray-600">
            <p>Count: {demoCount} | Has Unread: {hasUnread ? 'Yes' : 'No'}</p>
          </div>
        </div>
      </div>

      {/* Size Variations */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-6">Size Variations</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {(['sm', 'md', 'lg', 'xl'] as const).map(size => (
            <div key={size} className="text-center space-y-4">
              <h3 className="font-medium text-gray-700 capitalize">{size}</h3>
              <div className="flex justify-center space-x-4">
                <NotificationIcon size={size} />
                <NotificationIcon size={size} hasUnread />
                <NotificationIcon size={size} count={5} />
                <NotificationIcon size={size} count={99} />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Variant Styles */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-6">Style Variants</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {(['default', 'minimal', 'filled', 'outline'] as const).map(variant => (
            <div key={variant} className="text-center space-y-4">
              <h3 className="font-medium text-gray-700 capitalize">{variant}</h3>
              <div className="flex justify-center space-x-4">
                <NotificationIcon variant={variant} />
                <NotificationIcon variant={variant} hasUnread />
                <NotificationIcon variant={variant} count={3} />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* State Examples */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-6">Different States</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
          <div className="text-center space-y-3">
            <NotificationIcon />
            <p className="text-sm text-gray-600">Empty</p>
          </div>
          <div className="text-center space-y-3">
            <NotificationIcon hasUnread />
            <p className="text-sm text-gray-600">Unread Dot</p>
          </div>
          <div className="text-center space-y-3">
            <NotificationIcon count={1} />
            <p className="text-sm text-gray-600">Single</p>
          </div>
          <div className="text-center space-y-3">
            <NotificationIcon count={12} />
            <p className="text-sm text-gray-600">Multiple</p>
          </div>
          <div className="text-center space-y-3">
            <NotificationIcon count={150} />
            <p className="text-sm text-gray-600">99+ Max</p>
          </div>
          <div className="text-center space-y-3">
            <NotificationIcon disabled />
            <p className="text-sm text-gray-600">Disabled</p>
          </div>
        </div>
      </div>

      {/* Usage Examples */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-6">Usage Examples</h2>
        
        <div className="space-y-8">
          {/* Header Navigation */}
          <div className="bg-gray-50 rounded-xl p-6">
            <h3 className="font-medium text-gray-900 mb-4">Header Navigation</h3>
            <div className="flex items-center justify-between">
              <div className="text-lg font-semibold">App Name</div>
              <div className="flex items-center space-x-4">
                <NotificationIcon count={3} variant="minimal" />
                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
              </div>
            </div>
          </div>

          {/* Mobile Tab Bar */}
          <div className="bg-gray-50 rounded-xl p-6">
            <h3 className="font-medium text-gray-900 mb-4">Mobile Tab Bar</h3>
            <div className="flex justify-around items-center bg-white rounded-lg p-4 shadow-sm">
              <div className="text-center">
                <div className="w-6 h-6 bg-gray-300 rounded mx-auto mb-1"></div>
                <span className="text-xs text-gray-600">Home</span>
              </div>
              <div className="text-center">
                <NotificationIcon size="sm" count={7} variant="minimal" className="mx-auto mb-1" />
                <span className="text-xs text-gray-600">Alerts</span>
              </div>
              <div className="text-center">
                <div className="w-6 h-6 bg-gray-300 rounded mx-auto mb-1"></div>
                <span className="text-xs text-gray-600">Profile</span>
              </div>
            </div>
          </div>

          {/* Settings Panel */}
          <div className="bg-gray-50 rounded-xl p-6">
            <h3 className="font-medium text-gray-900 mb-4">Settings Panel</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                <span>Push Notifications</span>
                <NotificationIcon size="sm" variant="outline" />
              </div>
              <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                <span>Email Alerts</span>
                <NotificationIcon size="sm" hasUnread variant="outline" />
              </div>
              <div className="flex items-center justify-between p-3 bg-white rounded-lg">
                <span>SMS Notifications</span>
                <NotificationIcon size="sm" disabled variant="outline" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Implementation Guide */}
      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-8">
        <h2 className="text-2xl font-semibold text-blue-900 mb-6">Implementation Guide</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-semibold text-blue-800 mb-4">Basic Usage</h3>
            <div className="bg-white rounded-lg p-4 border border-blue-200">
              <pre className="text-sm text-gray-700 overflow-x-auto">
{`// Basic notification icon
<NotificationIcon />

// With unread indicator
<NotificationIcon hasUnread />

// With count
<NotificationIcon count={5} />

// Different sizes
<NotificationIcon size="lg" count={12} />

// Different variants
<NotificationIcon 
  variant="filled" 
  count={3} 
/>`}
              </pre>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold text-blue-800 mb-4">Advanced Features</h3>
            <div className="bg-white rounded-lg p-4 border border-blue-200">
              <pre className="text-sm text-gray-700 overflow-x-auto">
{`// With click handler
<NotificationIcon 
  count={7}
  onClick={() => openNotifications()}
/>

// Custom styling
<NotificationIcon 
  count={15}
  maxCount={50}
  showPulse={false}
  className="custom-class"
/>

// Disabled state
<NotificationIcon 
  disabled
  variant="outline"
/>`}
              </pre>
            </div>
          </div>
        </div>

        <div className="mt-8 p-4 bg-blue-100 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">Key Features:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Responsive design that scales across all device sizes</li>
            <li>• Multiple size options (sm, md, lg, xl) for different contexts</li>
            <li>• Four style variants (default, minimal, filled, outline)</li>
            <li>• Animated state changes with smooth transitions</li>
            <li>• Accessibility features with proper ARIA labels</li>
            <li>• Customizable count display with overflow handling (99+)</li>
            <li>• Pulse animations for urgent notifications</li>
            <li>• Disabled state support</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default NotificationIcon;