import React, { useState } from 'react';
import { Bell, Calendar, Clock, MapPin, Phone, Mail, AlertTriangle, CheckCircle, Info, X, ExternalLink, User, Building } from 'lucide-react';

interface NotificationMessageProps {
  type?: 'emergency' | 'update' | 'reminder' | 'announcement';
  className?: string;
}

const NotificationMessage: React.FC<NotificationMessageProps> = ({ 
  type = 'announcement',
  className = '' 
}) => {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  const getNotificationConfig = () => {
    switch (type) {
      case 'emergency':
        return {
          icon: AlertTriangle,
          iconColor: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          subject: 'URGENT: Flash Flood Warning - Immediate Action Required',
          priority: 'HIGH PRIORITY',
          priorityColor: 'bg-red-600 text-white'
        };
      case 'update':
        return {
          icon: Info,
          iconColor: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          subject: 'System Maintenance Update - Scheduled Downtime',
          priority: 'IMPORTANT',
          priorityColor: 'bg-blue-600 text-white'
        };
      case 'reminder':
        return {
          icon: Bell,
          iconColor: 'text-amber-600',
          bgColor: 'bg-amber-50',
          borderColor: 'border-amber-200',
          subject: 'Reminder: Emergency Preparedness Training Session',
          priority: 'REMINDER',
          priorityColor: 'bg-amber-600 text-white'
        };
      default:
        return {
          icon: CheckCircle,
          iconColor: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          subject: 'Community Safety Workshop - Registration Now Open',
          priority: 'ANNOUNCEMENT',
          priorityColor: 'bg-green-600 text-white'
        };
    }
  };

  const config = getNotificationConfig();
  const IconComponent = config.icon;

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      <div className={`${config.bgColor} ${config.borderColor} border-l-4 border-l-current rounded-xl shadow-lg overflow-hidden`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 pb-4">
          <div className="flex items-center space-x-4">
            <div className={`p-3 ${config.bgColor} rounded-xl border ${config.borderColor}`}>
              <IconComponent size={24} className={config.iconColor} />
            </div>
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <span className={`px-3 py-1 rounded-full text-xs font-bold ${config.priorityColor}`}>
                  {config.priority}
                </span>
                <span className="text-sm text-gray-500">
                  Notification ID: #DW-2024-001
                </span>
              </div>
              <h1 className="text-xl font-bold text-gray-900 leading-tight">
                {config.subject}
              </h1>
            </div>
          </div>
          <button
            onClick={() => setIsVisible(false)}
            className="p-2 hover:bg-white/50 rounded-lg transition-colors"
            title="Dismiss notification"
          >
            <X size={20} className="text-gray-500" />
          </button>
        </div>

        {/* Main Content */}
        <div className="px-6 pb-6">
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            {/* Message Body */}
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-3">Message Details</h2>
              <div className="prose prose-gray max-w-none">
                {type === 'emergency' && (
                  <div className="space-y-4">
                    <p className="text-gray-700 leading-relaxed">
                      The National Weather Service has issued a <strong>Flash Flood Warning</strong> for our area, effective immediately. Heavy rainfall is expected to continue for the next 6-8 hours, with potential for rapid water level rises in low-lying areas.
                    </p>
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <h3 className="font-semibold text-red-900 mb-2">Immediate Actions Required:</h3>
                      <ul className="text-red-800 space-y-1 text-sm">
                        <li>• Avoid driving through flooded roads</li>
                        <li>• Move to higher ground if in flood-prone areas</li>
                        <li>• Monitor local emergency broadcasts</li>
                        <li>• Prepare emergency supplies and evacuation routes</li>
                      </ul>
                    </div>
                  </div>
                )}

                {type === 'update' && (
                  <div className="space-y-4">
                    <p className="text-gray-700 leading-relaxed">
                      We will be performing scheduled system maintenance to improve platform performance and security. During this time, some features may be temporarily unavailable.
                    </p>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h3 className="font-semibold text-blue-900 mb-2">What to Expect:</h3>
                      <ul className="text-blue-800 space-y-1 text-sm">
                        <li>• Report submission may be temporarily disabled</li>
                        <li>• Chat features will remain operational</li>
                        <li>• Map viewing will continue to function normally</li>
                        <li>• All data will be preserved and secure</li>
                      </ul>
                    </div>
                  </div>
                )}

                {type === 'reminder' && (
                  <div className="space-y-4">
                    <p className="text-gray-700 leading-relaxed">
                      Don't forget about our upcoming Emergency Preparedness Training Session. This comprehensive workshop will cover essential disaster response skills and community coordination strategies.
                    </p>
                    <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                      <h3 className="font-semibold text-amber-900 mb-2">What You'll Learn:</h3>
                      <ul className="text-amber-800 space-y-1 text-sm">
                        <li>• First aid and emergency medical response</li>
                        <li>• Disaster communication protocols</li>
                        <li>• Community evacuation procedures</li>
                        <li>• Emergency supply management</li>
                      </ul>
                    </div>
                  </div>
                )}

                {type === 'announcement' && (
                  <div className="space-y-4">
                    <p className="text-gray-700 leading-relaxed">
                      We're excited to announce our new Community Safety Workshop series! These interactive sessions are designed to strengthen our community's disaster preparedness and response capabilities.
                    </p>
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <h3 className="font-semibold text-green-900 mb-2">Workshop Benefits:</h3>
                      <ul className="text-green-800 space-y-1 text-sm">
                        <li>• Hands-on emergency response training</li>
                        <li>• Networking with local emergency responders</li>
                        <li>• Free emergency preparedness kit</li>
                        <li>• Certification upon completion</li>
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Date, Time, and Location */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Calendar size={20} className="text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Date</p>
                  <p className="text-sm text-gray-600">
                    {type === 'emergency' ? 'January 15, 2024' : 'February 10, 2024'}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Clock size={20} className="text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Time</p>
                  <p className="text-sm text-gray-600">
                    {type === 'emergency' ? 'Effective Immediately' : 
                     type === 'update' ? '2:00 AM - 6:00 AM EST' :
                     type === 'reminder' ? '10:00 AM - 4:00 PM EST' :
                     '9:00 AM - 5:00 PM EST'}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <MapPin size={20} className="text-gray-500" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Location</p>
                  <p className="text-sm text-gray-600">
                    {type === 'emergency' ? 'Citywide Alert' : 
                     type === 'update' ? 'System-wide' :
                     'Community Center, Main Hall'}
                  </p>
                </div>
              </div>
            </div>

            {/* Deadline/Time-Sensitive Information */}
            {(type === 'reminder' || type === 'announcement') && (
              <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start space-x-3">
                  <Clock size={20} className="text-yellow-600 mt-0.5" />
                  <div>
                    <h3 className="font-semibold text-yellow-900 mb-1">Time-Sensitive Information</h3>
                    <p className="text-yellow-800 text-sm">
                      {type === 'reminder' 
                        ? 'Registration closes on February 8, 2024 at 11:59 PM EST. Limited seats available.'
                        : 'Early bird registration ends January 31, 2024. Register now to secure your spot and receive a 20% discount.'
                      }
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Contact Information */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="font-semibold text-gray-900 mb-4">Contact Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 text-blue-600 rounded-lg">
                      <Phone size={16} />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Emergency Hotline</p>
                      <p className="text-sm text-gray-600">1-800-DISASTER (**************)</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-100 text-green-600 rounded-lg">
                      <Mail size={16} />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Email Support</p>
                      <p className="text-sm text-gray-600"><EMAIL></p>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-purple-100 text-purple-600 rounded-lg">
                      <User size={16} />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Emergency Coordinator</p>
                      <p className="text-sm text-gray-600">Sarah Chen, Director</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-orange-100 text-orange-600 rounded-lg">
                      <Building size={16} />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">Office Hours</p>
                      <p className="text-sm text-gray-600">24/7 Emergency Response</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="border-t border-gray-200 pt-6 mt-6">
              <div className="flex flex-col sm:flex-row gap-3">
                {type === 'emergency' && (
                  <>
                    <button className="flex-1 bg-red-600 text-white py-3 px-6 rounded-xl hover:bg-red-700 transition-colors font-medium flex items-center justify-center">
                      <AlertTriangle size={16} className="mr-2" />
                      View Emergency Map
                    </button>
                    <button className="flex-1 border border-red-300 text-red-700 py-3 px-6 rounded-xl hover:bg-red-50 transition-colors font-medium">
                      Get Safety Updates
                    </button>
                  </>
                )}

                {(type === 'reminder' || type === 'announcement') && (
                  <>
                    <button className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-xl hover:bg-blue-700 transition-colors font-medium flex items-center justify-center">
                      <ExternalLink size={16} className="mr-2" />
                      Register Now
                    </button>
                    <button className="flex-1 border border-blue-300 text-blue-700 py-3 px-6 rounded-xl hover:bg-blue-50 transition-colors font-medium">
                      Learn More
                    </button>
                  </>
                )}

                {type === 'update' && (
                  <button className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-xl hover:bg-blue-700 transition-colors font-medium flex items-center justify-center">
                    <Info size={16} className="mr-2" />
                    View System Status
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Professional Closing */}
          <div className="mt-6 p-4 bg-white rounded-xl border border-gray-200">
            <div className="text-center">
              <p className="text-gray-700 mb-2">
                Thank you for your attention to this important matter. Your safety and preparedness are our top priorities.
              </p>
              <p className="text-sm text-gray-600">
                Best regards,<br />
                <span className="font-medium">The DisasterWatch Emergency Response Team</span><br />
                <span className="text-xs">Community Safety & Disaster Preparedness Division</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationMessage;