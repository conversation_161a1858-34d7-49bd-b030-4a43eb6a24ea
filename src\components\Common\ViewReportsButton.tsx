import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Eye, ArrowRight, FileText, MapPin } from 'lucide-react';

interface ViewReportsButtonProps {
  className?: string;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  animated?: boolean;
}

const ViewReportsButton: React.FC<ViewReportsButtonProps> = ({
  className = '',
  variant = 'primary',
  size = 'md',
  showIcon = true,
  animated = true
}) => {
  const navigate = useNavigate();
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    navigate('/reports');
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return 'bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 shadow-sm hover:shadow-lg';
      case 'secondary':
        return 'bg-gradient-to-r from-gray-500 to-gray-600 text-white hover:from-gray-600 hover:to-gray-700 shadow-sm hover:shadow-lg';
      case 'outline':
        return 'border-2 border-blue-500 text-blue-600 hover:bg-blue-500 hover:text-white';
      default:
        return 'bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 shadow-sm hover:shadow-lg';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-4 py-2 text-sm';
      case 'md':
        return 'px-6 py-3 text-base';
      case 'lg':
        return 'px-8 py-4 text-lg';
      default:
        return 'px-6 py-3 text-base';
    }
  };

  return (
    <button
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={`
        ${getVariantClasses()}
        ${getSizeClasses()}
        ${animated ? 'transition-all duration-300 transform hover:-translate-y-1' : 'transition-colors'}
        rounded-xl font-semibold flex items-center space-x-2 group
        ${className}
      `}
    >
      {showIcon && (
        <Eye 
          size={size === 'sm' ? 16 : size === 'lg' ? 24 : 20} 
          className={animated ? 'transition-transform group-hover:scale-110' : ''} 
        />
      )}
      <span>View Reports</span>
      {animated && (
        <ArrowRight 
          size={size === 'sm' ? 14 : size === 'lg' ? 20 : 16}
          className={`transition-transform duration-300 ${
            isHovered ? 'translate-x-1' : ''
          }`}
        />
      )}
    </button>
  );
};

// Alternative card-style button
export const ViewReportsCard: React.FC<{ className?: string }> = ({ className = '' }) => {
  const navigate = useNavigate();
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    navigate('/reports');
  };

  return (
    <div
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={`
        bg-white rounded-2xl shadow-sm border border-gray-200 p-6 cursor-pointer
        transition-all duration-300 hover:shadow-xl hover:-translate-y-2 group
        ${className}
      `}
    >
      <div className="flex items-center space-x-4 mb-4">
        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
          <FileText size={24} className="text-white" />
        </div>
        <div>
          <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
            View All Reports
          </h3>
          <p className="text-gray-600 text-sm">Browse disaster reports in your area</p>
        </div>
      </div>
      
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 text-sm text-gray-500">
          <div className="flex items-center space-x-1">
            <MapPin size={14} />
            <span>247 reports</span>
          </div>
          <div className="flex items-center space-x-1">
            <Eye size={14} />
            <span>Live updates</span>
          </div>
        </div>
        
        <ArrowRight 
          size={20} 
          className={`text-blue-500 transition-transform duration-300 ${
            isHovered ? 'translate-x-2' : ''
          }`}
        />
      </div>
    </div>
  );
};

export default ViewReportsButton;