import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { ShieldCheck, Menu, X, User, LogOut, Settings, Shield } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import LoginModal from '../Auth/LoginModal';

const Header: React.FC = () => {
  const { user, logout, isAdmin, isEditor, isUser, getRedirectPath } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);

  // Auto-redirect users to their appropriate dashboard if they're on wrong routes
  useEffect(() => {
    if (user) {
      const currentPath = location.pathname;
      const correctPath = getRedirectPath();
      
      // Prevent infinite redirects and allow access to public pages
      const publicPaths = ['/', '/partners', '/resources', '/notifications', '/reports'];
      const isPublicPath = publicPaths.some(path => currentPath === path || currentPath.startsWith('/reports/'));
      
      // Redirect if user is on wrong dashboard
      if (!isPublicPath) {
        if (isAdmin() && !currentPath.startsWith('/admin')) {
          navigate('/admin', { replace: true });
        } else if (isEditor() && !currentPath.startsWith('/editor')) {
          navigate('/editor', { replace: true });
        } else if (isUser() && (currentPath.startsWith('/admin') || currentPath.startsWith('/editor'))) {
          navigate('/dashboard', { replace: true });
        }
      }
    }
  }, [user, location.pathname, navigate, isAdmin, isEditor, isUser, getRedirectPath]);

  const isActivePage = (path: string) => location.pathname === path;

  // Dynamic navigation based on user role
  const getNavItems = () => {
    const baseItems = [
      { name: 'Home', path: '/' },
      { name: 'View Reports', path: '/reports' },
      { name: 'Resources', path: '/resources' },
      { name: 'Partners', path: '/partners' },
    ];

    if (user) {
      if (isUser()) {
        return [
          ...baseItems,
          { name: 'Report Impact', path: '/report/new' },
          { name: 'My Dashboard', path: '/dashboard' }
        ];
      }
      // Admins and Editors don't see user-specific nav items in header
      return baseItems;
    }

    return baseItems;
  };

  const navItems = getNavItems();

  const getRoleBadge = () => {
    if (!user) return null;
    
    const roleConfig = {
      admin: { label: 'Admin', color: 'bg-red-100 text-red-700' },
      editor: { label: 'Editor', color: 'bg-green-100 text-green-700' },
      user: { label: 'User', color: 'bg-blue-100 text-blue-700' }
    };

    const config = roleConfig[user.role];
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const getDashboardLink = () => {
    if (!user) return null;
    
    switch (user.role) {
      case 'admin':
        return { path: '/admin', label: 'Admin Panel', icon: Shield, color: 'from-red-500 to-red-600' };
      case 'editor':
        return { path: '/editor', label: 'Editor Panel', icon: Shield, color: 'from-green-500 to-green-600' };
      case 'user':
        return { path: '/dashboard', label: 'Dashboard', icon: User, color: 'from-blue-500 to-blue-600' };
      default:
        return null;
    }
  };

  const dashboardLink = getDashboardLink();

  return (
    <>
      <header className="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity">
              <div className="p-2 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-xl shadow-sm">
                <ShieldCheck size={24} />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">DisasterWatch</h1>
                <p className="text-xs text-gray-500 -mt-1">Community Reporting</p>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-2">
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.path}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    isActivePage(item.path)
                      ? 'bg-blue-50 text-blue-700 shadow-sm'
                      : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                >
                  {item.name}
                </Link>
              ))}
            </nav>

            {/* Auth Section */}
            <div className="hidden md:flex items-center space-x-4">
              {user ? (
                <div className="flex items-center space-x-3">
                  {/* Dashboard Link */}
                  {dashboardLink && (
                    <Link
                      to={dashboardLink.path}
                      className={`flex items-center space-x-2 px-3 py-2 bg-gradient-to-r ${dashboardLink.color} text-white rounded-lg hover:shadow-md transition-all duration-200 text-sm font-medium shadow-sm`}
                    >
                      <dashboardLink.icon size={16} />
                      <span>{dashboardLink.label}</span>
                    </Link>
                  )}

                  {/* User Menu */}
                  <div className="relative">
                    <button
                      onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                      className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <img
                        src={user.avatar || 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=40&h=40&dpr=1'}
                        alt={user.name}
                        className="w-8 h-8 rounded-full object-cover ring-2 ring-blue-100"
                      />
                      <div className="text-left">
                        <span className="text-sm font-medium text-gray-700 block">{user.name}</span>
                        {getRoleBadge()}
                      </div>
                    </button>
                    
                    {isUserMenuOpen && (
                      <div className="absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-lg border border-gray-100 py-2 z-50">
                        <div className="px-4 py-3 border-b border-gray-100">
                          <p className="text-sm font-medium text-gray-900">{user.name}</p>
                          <p className="text-xs text-gray-600">{user.email}</p>
                          <div className="mt-2 flex items-center space-x-2">
                            {getRoleBadge()}
                            <span className="text-xs text-gray-500">
                              {user.permissions.length} permissions
                            </span>
                          </div>
                        </div>
                        
                        {dashboardLink && (
                          <Link
                            to={dashboardLink.path}
                            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                            onClick={() => setIsUserMenuOpen(false)}
                          >
                            <dashboardLink.icon size={16} className="mr-3 text-gray-400" />
                            {dashboardLink.label}
                          </Link>
                        )}

                        <Link
                          to="/settings"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                          onClick={() => setIsUserMenuOpen(false)}
                        >
                          <Settings size={16} className="mr-3 text-gray-400" />
                          Settings
                        </Link>

                        <div className="border-t border-gray-100 mt-2 pt-2">
                          <button
                            onClick={() => {
                              logout();
                              setIsUserMenuOpen(false);
                              navigate('/');
                            }}
                            className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                          >
                            <LogOut size={16} className="mr-3" />
                            Sign Out
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <button
                  onClick={() => setIsLoginModalOpen(true)}
                  className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-2 rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium shadow-sm"
                >
                  Login
                </button>
              )}
            </div>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 rounded-md text-gray-600 hover:text-blue-600 hover:bg-gray-50 transition-colors"
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden bg-white border-t border-gray-100">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.path}
                  className={`block px-3 py-2 rounded-md text-base font-medium transition-colors ${
                    isActivePage(item.path)
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              
              <div className="border-t border-gray-100 pt-3 mt-3">
                {user ? (
                  <div className="space-y-1">
                    <div className="px-3 py-2 text-sm">
                      <div className="flex items-center space-x-3 mb-2">
                        <img
                          src={user.avatar || 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=40&h=40&dpr=1'}
                          alt={user.name}
                          className="w-8 h-8 rounded-full object-cover"
                        />
                        <div>
                          <p className="font-medium text-gray-900">{user.name}</p>
                          {getRoleBadge()}
                        </div>
                      </div>
                    </div>
                    
                    {dashboardLink && (
                      <Link
                        to={dashboardLink.path}
                        className="block px-3 py-2 text-base font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 transition-colors"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {dashboardLink.label}
                      </Link>
                    )}

                    <button
                      onClick={() => {
                        logout();
                        setIsMenuOpen(false);
                        navigate('/');
                      }}
                      className="block w-full text-left px-3 py-2 text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-colors"
                    >
                      Logout
                    </button>
                  </div>
                ) : (
                  <button
                    onClick={() => {
                      setIsLoginModalOpen(true);
                      setIsMenuOpen(false);
                    }}
                    className="block w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white px-3 py-2 rounded-md text-base font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-200"
                  >
                    Login
                  </button>
                )}
              </div>
            </div>
          </div>
        )}
      </header>

      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
    </>
  );
};

export default Header;