import React, { useEffect, useRef, useState } from 'react';
import L from 'leaflet';
import { Search, MapPin, Navigation, X, Loader } from 'lucide-react';
import 'leaflet/dist/leaflet.css';

// Fix for default markers
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface LocationPickerProps {
  onLocationSelect: (lat: number, lng: number, address: string) => void;
  selectedLocation?: { lat: number; lng: number; address: string } | null;
  height?: string;
}

interface SearchResult {
  display_name: string;
  lat: string;
  lon: string;
  place_id: string;
  type: string;
  importance: number;
}

const LocationPicker: React.FC<LocationPickerProps> = ({
  onLocationSelect,
  selectedLocation,
  height = '400px'
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const markerRef = useRef<L.Marker | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [mapReady, setMapReady] = useState(false);
  
  // Search functionality state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [isGettingLocation, setIsGettingLocation] = useState(false);

  // Debounce search
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  // Reverse geocoding function
  const reverseGeocode = async (lat: number, lng: number): Promise<string> => {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`
      );
      const data = await response.json();
      
      if (data && data.display_name) {
        return data.display_name;
      }
      return `${lat.toFixed(4)}, ${lng.toFixed(4)}`;
    } catch (error) {
      console.error('Reverse geocoding failed:', error);
      return `${lat.toFixed(4)}, ${lng.toFixed(4)}`;
    }
  };

  // Forward geocoding (search) function
  const searchLocations = async (query: string): Promise<SearchResult[]> => {
    if (!query.trim() || query.length < 3) return [];
    
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=5&addressdetails=1&countrycodes=us`
      );
      const data = await response.json();
      return data || [];
    } catch (error) {
      console.error('Search failed:', error);
      throw new Error('Search failed. Please try again.');
    }
  };

  // Handle search input change with debouncing
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    setSearchError(null);
    
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (value.trim().length < 3) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    searchTimeoutRef.current = setTimeout(async () => {
      setIsSearching(true);
      try {
        const results = await searchLocations(value);
        setSearchResults(results);
        setShowSearchResults(true);
      } catch (error) {
        setSearchError(error instanceof Error ? error.message : 'Search failed');
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    }, 500);
  };

  // Handle search result selection
  const handleSearchResultSelect = async (result: SearchResult) => {
    const lat = parseFloat(result.lat);
    const lng = parseFloat(result.lon);
    
    setSearchQuery(result.display_name);
    setShowSearchResults(false);
    setSearchResults([]);
    
    // Update map and marker
    if (mapInstanceRef.current) {
      // Remove existing marker
      if (markerRef.current) {
        mapInstanceRef.current.removeLayer(markerRef.current);
      }

      // Add new marker
      const marker = L.marker([lat, lng]).addTo(mapInstanceRef.current);
      markerRef.current = marker;

      // Update popup
      marker.bindPopup(`
        <div class="p-3">
          <h3 class="font-semibold text-sm mb-2 text-gray-900">📍 Selected Location</h3>
          <p class="text-xs text-gray-600 mb-2">${result.display_name}</p>
          <p class="text-xs text-gray-500">Lat: ${lat.toFixed(6)}, Lng: ${lng.toFixed(6)}</p>
        </div>
      `).openPopup();

      // Center map on location
      mapInstanceRef.current.setView([lat, lng], 15);
    }

    // Call the callback
    onLocationSelect(lat, lng, result.display_name);
  };

  // Get current location
  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by this browser.');
      return;
    }

    setIsGettingLocation(true);
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;
        
        try {
          const address = await reverseGeocode(latitude, longitude);
          
          if (mapInstanceRef.current) {
            // Remove existing marker
            if (markerRef.current) {
              mapInstanceRef.current.removeLayer(markerRef.current);
            }

            // Add new marker
            const marker = L.marker([latitude, longitude]).addTo(mapInstanceRef.current);
            markerRef.current = marker;

            // Update popup
            marker.bindPopup(`
              <div class="p-3">
                <h3 class="font-semibold text-sm mb-2 text-gray-900">📍 Current Location</h3>
                <p class="text-xs text-gray-600 mb-2">${address}</p>
                <p class="text-xs text-gray-500">Lat: ${latitude.toFixed(6)}, Lng: ${longitude.toFixed(6)}</p>
              </div>
            `).openPopup();

            // Center map on location
            mapInstanceRef.current.setView([latitude, longitude], 15);
          }

          setSearchQuery(address);
          onLocationSelect(latitude, longitude, address);
        } catch (error) {
          console.error('Error getting address:', error);
        } finally {
          setIsGettingLocation(false);
        }
      },
      (error) => {
        console.error('Geolocation error:', error);
        setIsGettingLocation(false);
        
        let errorMessage = 'Unable to get your location.';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied. Please enable location permissions.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information unavailable.';
            break;
          case error.TIMEOUT:
            errorMessage = 'Location request timed out.';
            break;
        }
        alert(errorMessage);
      },
      {
        timeout: 10000,
        enableHighAccuracy: true,
        maximumAge: 300000 // 5 minutes
      }
    );
  };

  // Clear search
  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setShowSearchResults(false);
    setSearchError(null);
  };

  useEffect(() => {
    if (!mapRef.current) return;

    // Small delay to ensure the container is properly sized
    const initializeMap = () => {
      try {
        // Initialize map with explicit container check
        const map = L.map(mapRef.current!, {
          center: [39.8283, -98.5795],
          zoom: 4,
          zoomControl: true,
          scrollWheelZoom: true,
          doubleClickZoom: true,
          boxZoom: true,
          keyboard: true,
          dragging: true,
          touchZoom: true
        });
        
        mapInstanceRef.current = map;

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '© OpenStreetMap contributors',
          maxZoom: 19
        }).addTo(map);

        // Force map to invalidate size after a short delay
        setTimeout(() => {
          if (mapInstanceRef.current) {
            mapInstanceRef.current.invalidateSize();
            setMapReady(true);
          }
        }, 100);

        // Add click handler for location selection
        map.on('click', async (e) => {
          const { lat, lng } = e.latlng;
          setIsLoading(true);

          // Remove existing marker
          if (markerRef.current) {
            map.removeLayer(markerRef.current);
          }

          // Add new marker
          const marker = L.marker([lat, lng]).addTo(map);
          markerRef.current = marker;

          // Get address from coordinates
          const address = await reverseGeocode(lat, lng);
          setIsLoading(false);

          // Update popup with address
          marker.bindPopup(`
            <div class="p-3">
              <h3 class="font-semibold text-sm mb-2 text-gray-900">📍 Selected Location</h3>
              <p class="text-xs text-gray-600 mb-2">${address}</p>
              <p class="text-xs text-gray-500">Lat: ${lat.toFixed(6)}, Lng: ${lng.toFixed(6)}</p>
            </div>
          `).openPopup();

          // Update search query
          setSearchQuery(address);

          // Call the callback
          onLocationSelect(lat, lng, address);
        });

        // Try to get user's current location
        if (navigator.geolocation) {
          navigator.geolocation.getCurrentPosition(
            (position) => {
              const { latitude, longitude } = position.coords;
              // Check if map instance is still valid before calling setView
              if (mapInstanceRef.current) {
                mapInstanceRef.current.setView([latitude, longitude], 10);
              }
            },
            (error) => {
              console.log('Geolocation error:', error);
              // Keep default view
            },
            {
              timeout: 10000,
              enableHighAccuracy: false
            }
          );
        }

      } catch (error) {
        console.error('Map initialization failed:', error);
      }
    };

    // Initialize map after a small delay to ensure DOM is ready
    const timeoutId = setTimeout(initializeMap, 50);

    return () => {
      clearTimeout(timeoutId);
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [onLocationSelect]);

  // Update marker when selectedLocation changes
  useEffect(() => {
    if (!mapInstanceRef.current || !mapReady) return;

    if (selectedLocation) {
      // Remove existing marker
      if (markerRef.current) {
        mapInstanceRef.current.removeLayer(markerRef.current);
      }

      // Add marker at selected location
      const marker = L.marker([selectedLocation.lat, selectedLocation.lng])
        .addTo(mapInstanceRef.current);
      
      marker.bindPopup(`
        <div class="p-3">
          <h3 class="font-semibold text-sm mb-2 text-gray-900">📍 Selected Location</h3>
          <p class="text-xs text-gray-600 mb-2">${selectedLocation.address}</p>
          <p class="text-xs text-gray-500">Lat: ${selectedLocation.lat.toFixed(6)}, Lng: ${selectedLocation.lng.toFixed(6)}</p>
        </div>
      `);

      markerRef.current = marker;
      mapInstanceRef.current.setView([selectedLocation.lat, selectedLocation.lng], 12);
      setSearchQuery(selectedLocation.address);
    }
  }, [selectedLocation, mapReady]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return (
    <>
      <style>{`
        .leaflet-container {
          height: ${height};
          width: 100%;
          z-index: 1;
        }
        .leaflet-control-zoom {
          border: none !important;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
        }
        .leaflet-control-zoom a {
          background-color: white !important;
          border: 1px solid #ccc !important;
          color: #333 !important;
        }
        .leaflet-control-zoom a:hover {
          background-color: #f4f4f4 !important;
        }
        .leaflet-popup-content-wrapper {
          border-radius: 8px !important;
          box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
        }
      `}</style>
      
      <div className="space-y-4">
        {/* Search Controls */}
        <div className="bg-white rounded-xl border border-gray-200 p-4 shadow-sm">
          <div className="flex flex-col sm:flex-row gap-3">
            {/* Search Input */}
            <div className="flex-1 relative">
              <div className="relative">
                <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  placeholder="Search for a location (city, address, landmark...)"
                  className="w-full pl-10 pr-10 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                />
                {searchQuery && (
                  <button
                    onClick={clearSearch}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <X size={16} />
                  </button>
                )}
                {isSearching && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <Loader size={16} className="animate-spin text-blue-500" />
                  </div>
                )}
              </div>

              {/* Search Results Dropdown */}
              {showSearchResults && searchResults.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-xl shadow-lg z-20 max-h-60 overflow-y-auto">
                  {searchResults.map((result, index) => (
                    <button
                      key={result.place_id}
                      onClick={() => handleSearchResultSelect(result)}
                      className="w-full text-left px-4 py-3 hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0"
                    >
                      <div className="flex items-start space-x-3">
                        <MapPin size={16} className="text-blue-500 mt-0.5 flex-shrink-0" />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {result.display_name.split(',')[0]}
                          </p>
                          <p className="text-xs text-gray-500 truncate">
                            {result.display_name}
                          </p>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}

              {/* Search Error */}
              {searchError && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-red-50 border border-red-200 rounded-xl p-3 z-20">
                  <p className="text-sm text-red-700">{searchError}</p>
                </div>
              )}

              {/* No Results */}
              {showSearchResults && searchResults.length === 0 && !isSearching && searchQuery.length >= 3 && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-gray-50 border border-gray-200 rounded-xl p-3 z-20">
                  <p className="text-sm text-gray-600">No locations found. Try a different search term.</p>
                </div>
              )}
            </div>

            {/* Current Location Button */}
            <button
              onClick={getCurrentLocation}
              disabled={isGettingLocation}
              className="flex items-center space-x-2 px-4 py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors whitespace-nowrap"
            >
              {isGettingLocation ? (
                <Loader size={16} className="animate-spin" />
              ) : (
                <Navigation size={16} />
              )}
              <span className="hidden sm:inline">Use Current Location</span>
              <span className="sm:hidden">Current</span>
            </button>
          </div>
        </div>

        {/* Map Container */}
        <div className="relative">
          <div 
            ref={mapRef} 
            style={{ 
              height, 
              width: '100%',
              minHeight: '300px',
              backgroundColor: '#f8f9fa'
            }}
            className="rounded-xl border border-gray-300 cursor-crosshair"
          />
          {isLoading && (
            <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-xl z-10">
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"></div>
                <span className="text-sm text-gray-600">Getting address...</span>
              </div>
            </div>
          )}
          {!mapReady && (
            <div className="absolute inset-0 bg-gray-100 flex items-center justify-center rounded-xl">
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"></div>
                <span className="text-sm text-gray-600">Loading map...</span>
              </div>
            </div>
          )}
          
          {/* Instructions Overlay */}
          <div className="absolute top-4 left-4 bg-white rounded-xl shadow-lg p-4 max-w-xs z-10 border border-gray-200">
            <h4 className="font-semibold text-sm text-gray-900 mb-2 flex items-center">
              📍 Pin Your Location
            </h4>
            <div className="text-xs text-gray-600 space-y-1">
              <p>• Search for a location above</p>
              <p>• Click anywhere on the map</p>
              <p>• Use "Current Location" button</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default LocationPicker;