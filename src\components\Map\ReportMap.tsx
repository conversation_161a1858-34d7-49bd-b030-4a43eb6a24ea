import React, { useEffect, useRef } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { Report } from '../../types';

// Fix for default markers
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface ReportMapProps {
  reports: Report[];
  selectedReport?: Report | null;
  onReportSelect?: (report: Report) => void;
  height?: string;
}

const ReportMap: React.FC<ReportMapProps> = ({
  reports,
  selectedReport,
  onReportSelect,
  height = '400px'
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const markersRef = useRef<L.Marker[]>([]);

  useEffect(() => {
    if (!mapRef.current) return;

    // Initialize map
    const map = L.map(mapRef.current).setView([39.8283, -98.5795], 4);
    mapInstanceRef.current = map;

    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (!mapInstanceRef.current) return;

    // Clear existing markers
    markersRef.current.forEach(marker => marker.remove());
    markersRef.current = [];

    // Add new markers
    reports.forEach(report => {
      const marker = L.marker([report.location.lat, report.location.lng])
        .addTo(mapInstanceRef.current!);

      // Create popup content with proper event handling
      const popupDiv = document.createElement('div');
      popupDiv.className = 'p-3 min-w-[280px]';
      popupDiv.innerHTML = `
        <div>
          <h3 class="font-semibold text-lg mb-2 text-gray-900">${report.title}</h3>
          <div class="flex items-center mb-2">
            <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium mr-2">
              ${report.disasterDetail}
            </span>
            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
              ${report.status.charAt(0).toUpperCase() + report.status.slice(1)}
            </span>
          </div>
          <p class="text-sm text-gray-600 mb-3 line-clamp-3">${report.description.substring(0, 120)}${report.description.length > 120 ? '...' : ''}</p>
          <div class="flex items-center justify-between">
            <div class="text-xs text-gray-500">
              <div class="flex items-center mb-1">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                </svg>
                ${report.location.address}
              </div>
              <div class="flex items-center">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                </svg>
                ${new Date(report.createdAt).toLocaleDateString()}
              </div>
            </div>
            <button 
              class="view-details-btn bg-gradient-to-r from-blue-500 to-blue-600 text-white px-3 py-1.5 rounded-lg text-sm font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-200"
              data-report-id="${report.id}"
            >
              View Details
            </button>
          </div>
        </div>
      `;

      // Add click event listener to the button
      const viewDetailsBtn = popupDiv.querySelector('.view-details-btn');
      if (viewDetailsBtn) {
        viewDetailsBtn.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          // Navigate to report detail page
          window.location.href = `/reports/${report.id}`;
        });
      }

      marker.bindPopup(popupDiv, {
        maxWidth: 300,
        className: 'custom-popup'
      });

      // Add click handler for marker selection
      marker.on('click', () => {
        if (onReportSelect) {
          onReportSelect(report);
        }
      });

      markersRef.current.push(marker);
    });

    // Fit bounds to show all markers
    if (reports.length > 0) {
      const group = new L.featureGroup(markersRef.current);
      mapInstanceRef.current.fitBounds(group.getBounds().pad(0.1));
    }
  }, [reports, onReportSelect]);

  // Highlight selected report
  useEffect(() => {
    if (!selectedReport || !mapInstanceRef.current) return;

    const selectedMarker = markersRef.current.find(marker => {
      const pos = marker.getLatLng();
      return pos.lat === selectedReport.location.lat && pos.lng === selectedReport.location.lng;
    });

    if (selectedMarker) {
      selectedMarker.openPopup();
      mapInstanceRef.current.setView([selectedReport.location.lat, selectedReport.location.lng], 10);
    }
  }, [selectedReport]);

  return (
    <>
      <style>{`
        .custom-popup .leaflet-popup-content-wrapper {
          border-radius: 12px;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .custom-popup .leaflet-popup-content {
          margin: 0;
          line-height: 1.4;
        }
        .custom-popup .leaflet-popup-tip {
          background: white;
        }
        .line-clamp-3 {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
      <div 
        ref={mapRef} 
        style={{ height, width: '100%' }}
        className="rounded-xl border border-gray-200"
      />
    </>
  );
};

export default ReportMap;