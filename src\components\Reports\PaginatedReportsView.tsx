import React, { useState, useEffect, useMemo } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronsLeft, 
  ChevronsRight,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Eye,
  Edit,
  Trash2,
  Calendar,
  User,
  MapPin,
  AlertTriangle,
  CheckCircle,
  Clock,
  Search,
  Filter,
  Download,
  RefreshCw,
  Grid,
  List,
  MoreHorizontal
} from 'lucide-react';
import { mockReports } from '../../data/mockData';
import { Report } from '../../types';
import { format } from 'date-fns';
import LoadingSpinner from '../Common/LoadingSpinner';

interface PaginatedReportsViewProps {
  showFilters?: boolean;
  showActions?: boolean;
  compactView?: boolean;
}

type SortField = 'title' | 'createdAt' | 'status' | 'reporterName' | 'disasterType' | 'location';
type SortDirection = 'asc' | 'desc';
type ViewMode = 'table' | 'grid';

const PaginatedReportsView: React.FC<PaginatedReportsViewProps> = ({
  showFilters = true,
  showActions = true,
  compactView = false
}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedReports, setSelectedReports] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>('table');

  // Pagination state from URL parameters
  const currentPage = parseInt(searchParams.get('page') || '1', 10);
  const itemsPerPage = parseInt(searchParams.get('limit') || '25', 10);
  const sortField = (searchParams.get('sort') || 'createdAt') as SortField;
  const sortDirection = (searchParams.get('order') || 'desc') as SortDirection;
  const searchTerm = searchParams.get('search') || '';
  const statusFilter = searchParams.get('status') || '';
  const typeFilter = searchParams.get('type') || '';

  // Mock data with additional metrics
  const reportsWithMetrics = useMemo(() => {
    return mockReports.map(report => ({
      ...report,
      metrics: {
        views: Math.floor(Math.random() * 500) + 50,
        assistanceOffers: report.assistanceLog.length,
        shares: Math.floor(Math.random() * 50) + 5,
        priority: report.status === 'verified' ? 'High' : report.status === 'pending' ? 'Medium' : 'Low'
      }
    }));
  }, []);

  // Filtering and sorting logic
  const filteredAndSortedReports = useMemo(() => {
    let filtered = reportsWithMetrics.filter(report => {
      const matchesSearch = !searchTerm || 
        report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.location.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.reporterName.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = !statusFilter || report.status === statusFilter;
      const matchesType = !typeFilter || report.disasterDetail === typeFilter;
      
      return matchesSearch && matchesStatus && matchesType;
    });

    // Sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortField) {
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'createdAt':
          aValue = a.createdAt.getTime();
          bValue = b.createdAt.getTime();
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'reporterName':
          aValue = a.reporterName.toLowerCase();
          bValue = b.reporterName.toLowerCase();
          break;
        case 'disasterType':
          aValue = a.disasterDetail.toLowerCase();
          bValue = b.disasterDetail.toLowerCase();
          break;
        case 'location':
          aValue = a.location.address.toLowerCase();
          bValue = b.location.address.toLowerCase();
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [reportsWithMetrics, searchTerm, statusFilter, typeFilter, sortField, sortDirection]);

  // Pagination calculations
  const totalItems = filteredAndSortedReports.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
  const currentItems = filteredAndSortedReports.slice(startIndex, endIndex);

  // URL parameter updates
  const updateSearchParams = (updates: Record<string, string | number>) => {
    const newParams = new URLSearchParams(searchParams);
    Object.entries(updates).forEach(([key, value]) => {
      if (value === '' || value === null || value === undefined) {
        newParams.delete(key);
      } else {
        newParams.set(key, value.toString());
      }
    });
    setSearchParams(newParams);
  };

  // Simulate loading state
  useEffect(() => {
    setIsLoading(true);
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 300);
    return () => clearTimeout(timer);
  }, [currentPage, itemsPerPage, sortField, sortDirection, searchTerm, statusFilter, typeFilter]);

  // Handlers
  const handlePageChange = (page: number) => {
    updateSearchParams({ page });
  };

  const handleItemsPerPageChange = (limit: number) => {
    updateSearchParams({ limit, page: 1 });
  };

  const handleSort = (field: SortField) => {
    const newDirection = sortField === field && sortDirection === 'asc' ? 'desc' : 'asc';
    updateSearchParams({ sort: field, order: newDirection, page: 1 });
  };

  const handleSearch = (term: string) => {
    updateSearchParams({ search: term, page: 1 });
  };

  const handleFilterChange = (filterType: string, value: string) => {
    updateSearchParams({ [filterType]: value, page: 1 });
  };

  const handleSelectReport = (reportId: string) => {
    setSelectedReports(prev => 
      prev.includes(reportId) 
        ? prev.filter(id => id !== reportId)
        : [...prev, reportId]
    );
  };

  const handleSelectAll = () => {
    if (selectedReports.length === currentItems.length) {
      setSelectedReports([]);
    } else {
      setSelectedReports(currentItems.map(report => report.id));
    }
  };

  const handleBulkAction = (action: string) => {
    console.log(`Bulk action: ${action} on reports:`, selectedReports);
    // Implement bulk actions here
    setSelectedReports([]);
    setShowBulkActions(false);
  };

  const handleDelete = (reportId: string) => {
    if (window.confirm('Are you sure you want to delete this report?')) {
      console.log('Deleting report:', reportId);
      // Implement delete logic here
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'verified':
        return <CheckCircle size={14} className="text-green-600" />;
      case 'pending':
        return <Clock size={14} className="text-yellow-600" />;
      case 'rejected':
        return <AlertTriangle size={14} className="text-red-600" />;
      default:
        return <Clock size={14} className="text-gray-600" />;
    }
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ArrowUpDown size={14} className="text-gray-400" />;
    }
    return sortDirection === 'asc' 
      ? <ArrowUp size={14} className="text-blue-600" />
      : <ArrowDown size={14} className="text-blue-600" />;
  };

  const renderPaginationControls = () => (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 p-6 bg-white border-t border-gray-200">
      {/* Items per page and info */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <label htmlFor="itemsPerPage" className="text-sm font-medium text-gray-700">
            Show:
          </label>
          <select
            id="itemsPerPage"
            value={itemsPerPage}
            onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
            className="border border-gray-300 rounded-lg px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value={10}>10</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
        </div>
        <div className="text-sm text-gray-600">
          Showing {startIndex + 1}-{endIndex} of {totalItems} reports
        </div>
      </div>

      {/* Pagination controls */}
      <div className="flex items-center space-x-2">
        <button
          onClick={() => handlePageChange(1)}
          disabled={currentPage === 1}
          className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="First page"
        >
          <ChevronsLeft size={16} />
        </button>
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Previous page"
        >
          <ChevronLeft size={16} />
        </button>

        {/* Page numbers */}
        <div className="flex items-center space-x-1">
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            let pageNum;
            if (totalPages <= 5) {
              pageNum = i + 1;
            } else if (currentPage <= 3) {
              pageNum = i + 1;
            } else if (currentPage >= totalPages - 2) {
              pageNum = totalPages - 4 + i;
            } else {
              pageNum = currentPage - 2 + i;
            }

            return (
              <button
                key={pageNum}
                onClick={() => handlePageChange(pageNum)}
                className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                  currentPage === pageNum
                    ? 'bg-blue-600 text-white'
                    : 'border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {pageNum}
              </button>
            );
          })}
        </div>

        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Next page"
        >
          <ChevronRight size={16} />
        </button>
        <button
          onClick={() => handlePageChange(totalPages)}
          disabled={currentPage === totalPages}
          className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          title="Last page"
        >
          <ChevronsRight size={16} />
        </button>
      </div>
    </div>
  );

  const renderTableView = () => (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead className="bg-gray-50 border-b border-gray-200">
          <tr>
            {showActions && (
              <th className="w-12 px-6 py-4">
                <input
                  type="checkbox"
                  checked={selectedReports.length === currentItems.length && currentItems.length > 0}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </th>
            )}
            <th className="px-6 py-4 text-left">
              <button
                onClick={() => handleSort('title')}
                className="flex items-center space-x-1 text-sm font-medium text-gray-700 hover:text-gray-900"
              >
                <span>Title</span>
                {getSortIcon('title')}
              </button>
            </th>
            <th className="px-6 py-4 text-left">
              <button
                onClick={() => handleSort('disasterType')}
                className="flex items-center space-x-1 text-sm font-medium text-gray-700 hover:text-gray-900"
              >
                <span>Type</span>
                {getSortIcon('disasterType')}
              </button>
            </th>
            <th className="px-6 py-4 text-left">
              <button
                onClick={() => handleSort('status')}
                className="flex items-center space-x-1 text-sm font-medium text-gray-700 hover:text-gray-900"
              >
                <span>Status</span>
                {getSortIcon('status')}
              </button>
            </th>
            <th className="px-6 py-4 text-left">
              <button
                onClick={() => handleSort('reporterName')}
                className="flex items-center space-x-1 text-sm font-medium text-gray-700 hover:text-gray-900"
              >
                <span>Reporter</span>
                {getSortIcon('reporterName')}
              </button>
            </th>
            <th className="px-6 py-4 text-left">
              <button
                onClick={() => handleSort('location')}
                className="flex items-center space-x-1 text-sm font-medium text-gray-700 hover:text-gray-900"
              >
                <span>Location</span>
                {getSortIcon('location')}
              </button>
            </th>
            <th className="px-6 py-4 text-left">
              <button
                onClick={() => handleSort('createdAt')}
                className="flex items-center space-x-1 text-sm font-medium text-gray-700 hover:text-gray-900"
              >
                <span>Date</span>
                {getSortIcon('createdAt')}
              </button>
            </th>
            <th className="px-6 py-4 text-left">
              <span className="text-sm font-medium text-gray-700">Metrics</span>
            </th>
            {showActions && (
              <th className="px-6 py-4 text-right">
                <span className="text-sm font-medium text-gray-700">Actions</span>
              </th>
            )}
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {currentItems.map((report) => (
            <tr key={report.id} className="hover:bg-gray-50 transition-colors">
              {showActions && (
                <td className="px-6 py-4">
                  <input
                    type="checkbox"
                    checked={selectedReports.includes(report.id)}
                    onChange={() => handleSelectReport(report.id)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </td>
              )}
              <td className="px-6 py-4">
                <div className="flex items-start space-x-3">
                  <img
                    src={report.photos[0]}
                    alt={report.title}
                    className="w-12 h-12 object-cover rounded-lg"
                  />
                  <div>
                    <Link
                      to={`/reports/${report.id}`}
                      className="font-medium text-gray-900 hover:text-blue-600 transition-colors"
                    >
                      {report.title}
                    </Link>
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                      {report.description.substring(0, 100)}...
                    </p>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4">
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                  {report.disasterDetail}
                </span>
              </td>
              <td className="px-6 py-4">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(report.status)}
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(report.status)}`}>
                    {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                  </span>
                </div>
              </td>
              <td className="px-6 py-4">
                <div className="flex items-center space-x-2">
                  <User size={14} className="text-gray-400" />
                  <span className="text-sm text-gray-900">{report.reporterName}</span>
                </div>
              </td>
              <td className="px-6 py-4">
                <div className="flex items-center space-x-2">
                  <MapPin size={14} className="text-gray-400" />
                  <span className="text-sm text-gray-900">
                    {report.location.address.split(',')[0]}
                  </span>
                </div>
              </td>
              <td className="px-6 py-4">
                <div className="flex items-center space-x-2">
                  <Calendar size={14} className="text-gray-400" />
                  <span className="text-sm text-gray-900">
                    {format(report.createdAt, 'MMM d, yyyy')}
                  </span>
                </div>
              </td>
              <td className="px-6 py-4">
                <div className="flex flex-col space-y-1">
                  <div className="flex items-center space-x-4 text-xs text-gray-600">
                    <span>{report.metrics.views} views</span>
                    <span>{report.metrics.assistanceOffers} offers</span>
                  </div>
                  <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                    report.metrics.priority === 'High' ? 'bg-red-100 text-red-800' :
                    report.metrics.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {report.metrics.priority}
                  </span>
                </div>
              </td>
              {showActions && (
                <td className="px-6 py-4">
                  <div className="flex items-center space-x-2">
                    <Link
                      to={`/reports/${report.id}`}
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                      title="View report"
                    >
                      <Eye size={16} />
                    </Link>
                    <button
                      className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                      title="Edit report"
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={() => handleDelete(report.id)}
                      className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                      title="Delete report"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  const renderGridView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {currentItems.map((report) => (
        <div key={report.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300">
          <div className="relative">
            <img
              src={report.photos[0]}
              alt={report.title}
              className="w-full h-48 object-cover"
            />
            <div className="absolute top-4 left-4">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                {report.disasterDetail}
              </span>
            </div>
            <div className="absolute top-4 right-4">
              <div className="flex items-center space-x-2">
                {getStatusIcon(report.status)}
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(report.status)}`}>
                  {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                </span>
              </div>
            </div>
            {showActions && (
              <div className="absolute bottom-4 right-4">
                <input
                  type="checkbox"
                  checked={selectedReports.includes(report.id)}
                  onChange={() => handleSelectReport(report.id)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>
            )}
          </div>
          
          <div className="p-6">
            <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
              {report.title}
            </h3>
            <p className="text-sm text-gray-600 mb-4 line-clamp-3">
              {report.description}
            </p>
            
            <div className="space-y-2 mb-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <User size={14} className="text-gray-400" />
                <span>{report.reporterName}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <MapPin size={14} className="text-gray-400" />
                <span>{report.location.address.split(',')[0]}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Calendar size={14} className="text-gray-400" />
                <span>{format(report.createdAt, 'MMM d, yyyy')}</span>
              </div>
            </div>

            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4 text-xs text-gray-600">
                <span>{report.metrics.views} views</span>
                <span>{report.metrics.assistanceOffers} offers</span>
              </div>
              <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                report.metrics.priority === 'High' ? 'bg-red-100 text-red-800' :
                report.metrics.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-green-100 text-green-800'
              }`}>
                {report.metrics.priority}
              </span>
            </div>

            {showActions && (
              <div className="flex items-center justify-between">
                <Link
                  to={`/reports/${report.id}`}
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                >
                  View Details
                </Link>
                <div className="flex items-center space-x-2">
                  <button
                    className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                    title="Edit report"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    onClick={() => handleDelete(report.id)}
                    className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                    title="Delete report"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );

  const renderEmptyState = () => (
    <div className="text-center py-16">
      <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <AlertTriangle size={32} className="text-gray-400" />
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">No reports found</h3>
      <p className="text-gray-600 mb-6">
        {searchTerm || statusFilter || typeFilter
          ? 'Try adjusting your search criteria or filters.'
          : 'No disaster reports have been submitted yet.'}
      </p>
      {(searchTerm || statusFilter || typeFilter) && (
        <button
          onClick={() => {
            updateSearchParams({ search: '', status: '', type: '', page: 1 });
          }}
          className="bg-blue-500 text-white px-6 py-3 rounded-xl hover:bg-blue-600 transition-colors font-medium"
        >
          Clear Filters
        </button>
      )}
    </div>
  );

  const renderErrorState = () => (
    <div className="text-center py-16">
      <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <AlertTriangle size={32} className="text-red-500" />
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">Error loading reports</h3>
      <p className="text-gray-600 mb-6">{error}</p>
      <button
        onClick={() => {
          setError(null);
          setIsLoading(true);
          setTimeout(() => setIsLoading(false), 300);
        }}
        className="bg-blue-500 text-white px-6 py-3 rounded-xl hover:bg-blue-600 transition-colors font-medium flex items-center mx-auto"
      >
        <RefreshCw size={16} className="mr-2" />
        Try Again
      </button>
    </div>
  );

  if (error) {
    return (
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100">
        {renderErrorState()}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
          <div>
            <h2 className="text-2xl font-semibold text-gray-900">
              Disaster Reports
            </h2>
            <p className="text-gray-600 mt-1">
              Manage and review community disaster reports
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('table')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'table' 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Table view"
              >
                <List size={16} />
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'grid' 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Grid view"
              >
                <Grid size={16} />
              </button>
            </div>
            
            <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <Download size={16} />
              <span>Export</span>
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="md:col-span-2">
              <div className="relative">
                <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  placeholder="Search reports, locations, or reporters..."
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                />
              </div>
            </div>
            
            <div>
              <select
                value={statusFilter}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="w-full px-3 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              >
                <option value="">All Statuses</option>
                <option value="verified">Verified</option>
                <option value="pending">Pending</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
            
            <div>
              <select
                value={typeFilter}
                onChange={(e) => handleFilterChange('type', e.target.value)}
                className="w-full px-3 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              >
                <option value="">All Types</option>
                <option value="Flash Flood">Flash Flood</option>
                <option value="Wildfire">Wildfire</option>
                <option value="Tornado">Tornado</option>
                <option value="Earthquake">Earthquake</option>
              </select>
            </div>
          </div>
        )}

        {/* Bulk Actions */}
        {selectedReports.length > 0 && showActions && (
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-xl">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-blue-900">
                {selectedReports.length} report{selectedReports.length !== 1 ? 's' : ''} selected
              </span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleBulkAction('verify')}
                  className="px-3 py-1 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                >
                  Verify
                </button>
                <button
                  onClick={() => handleBulkAction('reject')}
                  className="px-3 py-1 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
                >
                  Reject
                </button>
                <button
                  onClick={() => handleBulkAction('delete')}
                  className="px-3 py-1 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm"
                >
                  Delete
                </button>
                <button
                  onClick={() => setSelectedReports([])}
                  className="px-3 py-1 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                >
                  Clear
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center py-16">
            <LoadingSpinner size={32} />
            <span className="ml-3 text-gray-600">Loading reports...</span>
          </div>
        ) : totalItems === 0 ? (
          renderEmptyState()
        ) : (
          <>
            <div className={viewMode === 'table' ? '' : 'p-6'}>
              {viewMode === 'table' ? renderTableView() : renderGridView()}
            </div>
            {renderPaginationControls()}
          </>
        )}
      </div>
    </div>
  );
};

export default PaginatedReportsView;