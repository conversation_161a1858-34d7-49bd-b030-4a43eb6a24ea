import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { AdminUser, Permission, ContentSection, AnalyticsData, DashboardMetrics } from '../types/admin';

interface AdminContextType {
  adminUser: AdminUser | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  hasPermission: (resource: string, action: string) => boolean;
  analytics: AnalyticsData | null;
  metrics: DashboardMetrics | null;
  contentSections: ContentSection[];
  updateContent: (sectionId: string, content: any) => Promise<void>;
  publishContent: (sectionId: string) => Promise<void>;
  scheduleContent: (sectionId: string, publishAt: Date) => Promise<void>;
  isLoading: boolean;
}

const AdminContext = createContext<AdminContextType | undefined>(undefined);

export const useAdmin = () => {
  const context = useContext(AdminContext);
  if (!context) {
    throw new Error('useAdmin must be used within an AdminProvider');
  }
  return context;
};

interface AdminProviderProps {
  children: ReactNode;
}

export const AdminProvider: React.FC<AdminProviderProps> = ({ children }) => {
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [contentSections, setContentSections] = useState<ContentSection[]>([]);

  // Mock data initialization
  useEffect(() => {
    // Initialize mock analytics data
    setAnalytics({
      visitors: {
        total: 12450,
        unique: 8920,
        returning: 3530,
        trend: 12.5
      },
      engagement: {
        avgSessionDuration: 245,
        bounceRate: 32.4,
        pageViews: 45680,
        trend: 8.2
      },
      conversions: {
        signups: 892,
        reports: 247,
        rate: 7.2,
        trend: 15.3
      },
      traffic: {
        sources: [
          { name: 'Direct', value: 45, color: '#4A90E2' },
          { name: 'Search', value: 30, color: '#50C878' },
          { name: 'Social', value: 15, color: '#FFB74D' },
          { name: 'Referral', value: 10, color: '#9B59B6' }
        ],
        devices: [
          { name: 'Desktop', value: 55 },
          { name: 'Mobile', value: 35 },
          { name: 'Tablet', value: 10 }
        ],
        locations: [
          { country: 'United States', visitors: 5420 },
          { country: 'Canada', visitors: 1890 },
          { country: 'United Kingdom', visitors: 1240 },
          { country: 'Australia', visitors: 890 },
          { country: 'Germany', visitors: 650 }
        ]
      }
    });

    // Initialize mock metrics
    setMetrics({
      totalReports: 2847,
      verifiedReports: 2189,
      activeUsers: 12450,
      responseTime: '< 2hrs',
      systemHealth: 'excellent',
      recentActivity: [
        {
          id: '1',
          action: 'Content Updated',
          user: 'Sarah Chen',
          timestamp: new Date(Date.now() - 10 * 60 * 1000),
          details: 'Updated hero section text',
          type: 'content'
        },
        {
          id: '2',
          action: 'User Login',
          user: 'Mike Rodriguez',
          timestamp: new Date(Date.now() - 25 * 60 * 1000),
          details: 'Admin dashboard access',
          type: 'user'
        },
        {
          id: '3',
          action: 'Report Verified',
          user: 'System',
          timestamp: new Date(Date.now() - 45 * 60 * 1000),
          details: 'Flood report #2847 verified',
          type: 'system'
        }
      ]
    });

    // Initialize mock content sections
    setContentSections([
      {
        id: 'hero',
        type: 'hero',
        title: 'Hero Section',
        content: {
          headline: 'Unite Communities in Times of Crisis',
          subheadline: 'Report disasters, offer assistance, and build resilience together. Every voice matters in community recovery.',
          ctaText: 'Report an Impact',
          backgroundImage: '/hero-bg.jpg'
        },
        isPublished: true,
        lastModified: new Date(),
        modifiedBy: 'Sarah Chen',
        version: 1
      },
      {
        id: 'features',
        type: 'features',
        title: 'Features Section',
        content: {
          title: 'Everything You Need for Disaster Response',
          subtitle: 'Comprehensive tools and features designed to connect communities',
          features: [
            {
              title: 'Real-Time Reporting',
              description: 'Report disasters instantly with photos and location data',
              icon: 'AlertTriangle'
            }
          ]
        },
        isPublished: true,
        lastModified: new Date(),
        modifiedBy: 'Mike Rodriguez',
        version: 2
      }
    ]);
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock admin user
    const mockAdminUser: AdminUser = {
      id: 'admin-1',
      name: 'Sarah Chen',
      email: email,
      avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1',
      role: 'admin',
      permissions: [
        { id: '1', name: 'Manage Content', resource: 'content', action: 'update' },
        { id: '2', name: 'Publish Content', resource: 'content', action: 'create' },
        { id: '3', name: 'Manage Users', resource: 'users', action: 'update' },
        { id: '4', name: 'View Analytics', resource: 'analytics', action: 'read' }
      ],
      lastLogin: new Date(),
      isActive: true,
      createdAt: new Date('2023-01-01')
    };
    
    setAdminUser(mockAdminUser);
    setIsLoading(false);
  };

  const logout = () => {
    setAdminUser(null);
  };

  const hasPermission = (resource: string, action: string) => {
    if (!adminUser) return false;
    return adminUser.permissions.some(p => p.resource === resource && p.action === action);
  };

  const updateContent = async (sectionId: string, content: any) => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    setContentSections(prev => 
      prev.map(section => 
        section.id === sectionId 
          ? { 
              ...section, 
              content, 
              lastModified: new Date(),
              modifiedBy: adminUser?.name || 'Unknown',
              version: section.version + 1
            }
          : section
      )
    );
    setIsLoading(false);
  };

  const publishContent = async (sectionId: string) => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 300));
    
    setContentSections(prev => 
      prev.map(section => 
        section.id === sectionId 
          ? { ...section, isPublished: true, scheduledAt: undefined }
          : section
      )
    );
    setIsLoading(false);
  };

  const scheduleContent = async (sectionId: string, publishAt: Date) => {
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 300));
    
    setContentSections(prev => 
      prev.map(section => 
        section.id === sectionId 
          ? { ...section, scheduledAt: publishAt, isPublished: false }
          : section
      )
    );
    setIsLoading(false);
  };

  return (
    <AdminContext.Provider value={{
      adminUser,
      isAuthenticated: !!adminUser,
      login,
      logout,
      hasPermission,
      analytics,
      metrics,
      contentSections,
      updateContent,
      publishContent,
      scheduleContent,
      isLoading
    }}>
      {children}
    </AdminContext.Provider>
  );
};