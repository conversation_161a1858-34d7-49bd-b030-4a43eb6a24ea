import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { User } from '../types';

export type UserRole = 'admin' | 'editor' | 'user';

export interface AuthUser extends User {
  role: UserRole;
  permissions: string[];
  isVerified: boolean;
  lastLogin?: Date;
}

interface AuthContextType {
  user: AuthUser | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
  hasPermission: (permission: string) => boolean;
  isAdmin: () => boolean;
  isEditor: () => boolean;
  isUser: () => boolean;
  getRedirectPath: () => string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

// Mock user database with strict role separation
const MOCK_USERS = {
  // Admin users - Only admin dashboard access
  '<EMAIL>': {
    id: 'admin-1',
    name: 'Sarah Chen',
    email: '<EMAIL>',
    password: 'admin123',
    avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1',
    role: 'admin' as UserRole,
    permissions: [
      'admin.dashboard.view',
      'admin.users.manage',
      'admin.content.manage',
      'admin.analytics.view',
      'admin.settings.manage',
      'admin.system.report'
    ],
    isVerified: true,
    lastLogin: new Date()
  },
  '<EMAIL>': {
    id: 'admin-2',
    name: 'John Smith',
    email: '<EMAIL>',
    password: 'superadmin123',
    avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1',
    role: 'admin' as UserRole,
    permissions: [
      'admin.dashboard.view',
      'admin.users.manage',
      'admin.content.manage',
      'admin.analytics.view',
      'admin.settings.manage',
      'admin.system.report',
      'admin.security.manage'
    ],
    isVerified: true,
    lastLogin: new Date()
  },
  
  // Editor users - Only editor interface access
  '<EMAIL>': {
    id: 'editor-1',
    name: 'Mike Rodriguez',
    email: '<EMAIL>',
    password: 'editor123',
    avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1',
    role: 'editor' as UserRole,
    permissions: [
      'editor.dashboard.view',
      'editor.content.manage',
      'editor.reports.moderate',
      'editor.analytics.view'
    ],
    isVerified: true,
    lastLogin: new Date()
  },
  '<EMAIL>': {
    id: 'editor-2',
    name: 'Emily Watson',
    email: '<EMAIL>',
    password: 'editor123',
    avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1',
    role: 'editor' as UserRole,
    permissions: [
      'editor.dashboard.view',
      'editor.content.manage',
      'editor.reports.moderate'
    ],
    isVerified: true,
    lastLogin: new Date()
  },
  
  // Regular users - Only user interface access
  '<EMAIL>': {
    id: 'user-1',
    name: 'John Anderson',
    email: '<EMAIL>',
    password: 'password123',
    avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1',
    role: 'user' as UserRole,
    permissions: [
      'user.dashboard.view',
      'reports.create',
      'reports.view',
      'assistance.offer',
      'chat.access'
    ],
    isVerified: true,
    lastLogin: new Date()
  },
  '<EMAIL>': {
    id: 'user-2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    password: 'password123',
    avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1',
    role: 'user' as UserRole,
    permissions: [
      'user.dashboard.view',
      'reports.create',
      'reports.view',
      'assistance.offer',
      'chat.access'
    ],
    isVerified: true,
    lastLogin: new Date()
  }
};

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Check for existing session on mount
  useEffect(() => {
    const savedUser = localStorage.getItem('disasterwatch_user');
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        // Verify the user still exists and is valid
        const mockUser = Object.values(MOCK_USERS).find(u => u.id === userData.id);
        if (mockUser && userData.sessionToken) {
          setUser({
            id: mockUser.id,
            name: mockUser.name,
            email: mockUser.email,
            avatar: mockUser.avatar,
            role: mockUser.role,
            permissions: mockUser.permissions,
            isVerified: mockUser.isVerified,
            lastLogin: mockUser.lastLogin
          });
        } else {
          // Invalid session, clear storage
          localStorage.removeItem('disasterwatch_user');
        }
      } catch (error) {
        console.error('Error parsing saved user:', error);
        localStorage.removeItem('disasterwatch_user');
      }
    }
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockUser = MOCK_USERS[email as keyof typeof MOCK_USERS];
      
      if (!mockUser || mockUser.password !== password) {
        throw new Error('Invalid email or password');
      }

      if (!mockUser.isVerified) {
        throw new Error('Account not verified. Please check your email.');
      }

      // Create session token (in real app, this would come from server)
      const sessionToken = btoa(`${mockUser.id}-${Date.now()}-${Math.random()}`);
      
      const authUser: AuthUser = {
        id: mockUser.id,
        name: mockUser.name,
        email: mockUser.email,
        avatar: mockUser.avatar,
        role: mockUser.role,
        permissions: mockUser.permissions,
        isVerified: mockUser.isVerified,
        lastLogin: new Date()
      };

      // Save to localStorage (in real app, use secure session management)
      localStorage.setItem('disasterwatch_user', JSON.stringify({
        ...authUser,
        sessionToken
      }));

      setUser(authUser);
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    localStorage.removeItem('disasterwatch_user');
    setUser(null);
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    return user.permissions.includes(permission);
  };

  const isAdmin = (): boolean => {
    return user?.role === 'admin';
  };

  const isEditor = (): boolean => {
    return user?.role === 'editor';
  };

  const isUser = (): boolean => {
    return user?.role === 'user';
  };

  const getRedirectPath = (): string => {
    if (!user) return '/';
    
    switch (user.role) {
      case 'admin':
        return '/admin';
      case 'editor':
        return '/editor';
      case 'user':
        return '/dashboard';
      default:
        return '/';
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      login,
      logout,
      isLoading,
      hasPermission,
      isAdmin,
      isEditor,
      isUser,
      getRedirectPath
    }}>
      {children}
    </AuthContext.Provider>
  );
};