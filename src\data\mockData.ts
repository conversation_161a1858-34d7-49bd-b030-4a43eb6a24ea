import { Report, Partner, SafetyGuide } from '../types';

export const mockReports: Report[] = [
  {
    id: '1',
    title: 'Flooding in Downtown District',
    disasterType: 'Natural',
    disasterDetail: 'Flash Flood',
    description: 'Severe flooding has affected multiple residential areas after heavy rainfall. Water levels reached 3-4 feet in some streets, making them impassable.',
    location: {
      lat: 40.7589,
      lng: -73.9851,
      address: 'Manhattan, New York, NY'
    },
    photos: [
      'https://images.pexels.com/photos/1118873/pexels-photo-1118873.jpeg?auto=compress&cs=tinysrgb&w=800',
      'https://images.pexels.com/photos/1592119/pexels-photo-1592119.jpeg?auto=compress&cs=tinysrgb&w=800'
    ],
    assistanceNeeded: ['Emergency Shelter', 'Food & Water', 'Medical Aid'],
    assistanceDescription: 'Immediate need for temporary shelter for 15 families. Clean drinking water and medical supplies urgently required.',
    status: 'verified',
    reporterId: '2',
    reporterName: '<PERSON>',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
    assistanceLog: [
      {
        id: '1',
        providerId: '1',
        providerName: '<PERSON>',
        description: 'Provided emergency shelter coordination with Red Cross. Secured temporary housing for 8 families.',
        createdAt: new Date('2024-01-16'),
        endorsed: true
      }
    ]
  },
  {
    id: '2',
    title: 'Wildfire Damage Assessment',
    disasterType: 'Natural',
    disasterDetail: 'Wildfire',
    description: 'Fast-moving wildfire has damaged several residential properties and threatens surrounding forest areas.',
    location: {
      lat: 34.0522,
      lng: -118.2437,
      address: 'Los Angeles, CA'
    },
    photos: [
      'https://images.pexels.com/photos/266487/pexels-photo-266487.jpeg?auto=compress&cs=tinysrgb&w=800'
    ],
    assistanceNeeded: ['Evacuation Support', 'Pet Care', 'Transportation'],
    assistanceDescription: 'Need assistance with evacuation transportation for elderly residents and pet care services.',
    status: 'verified',
    reporterId: '3',
    reporterName: 'Mike Davis',
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-12'),
    assistanceLog: []
  },
  {
    id: '3',
    title: 'Tornado Aftermath Cleanup',
    disasterType: 'Natural',
    disasterDetail: 'Tornado',
    description: 'EF2 tornado caused significant damage to residential area. Multiple homes damaged, debris scattered throughout neighborhood.',
    location: {
      lat: 39.7392,
      lng: -104.9903,
      address: 'Denver, CO'
    },
    photos: [
      'https://images.pexels.com/photos/1118869/pexels-photo-1118869.jpeg?auto=compress&cs=tinysrgb&w=800'
    ],
    assistanceNeeded: ['Cleanup Volunteers', 'Construction Help', 'Financial Support'],
    assistanceDescription: 'Looking for volunteers to help with debris removal and basic repairs. Families also need financial assistance for rebuilding.',
    status: 'pending',
    reporterId: '4',
    reporterName: 'Lisa Wilson',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10'),
    assistanceLog: []
  }
];

export const mockPartners: Partner[] = [
  {
    id: '1',
    name: 'American Red Cross',
    type: 'NGO',
    description: 'Leading humanitarian organization providing emergency assistance, disaster relief and education.',
    website: 'https://redcross.org',
    logo: 'https://images.pexels.com/photos/6646918/pexels-photo-6646918.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1'
  },
  {
    id: '2',
    name: 'FEMA',
    type: 'Government',
    description: 'Federal Emergency Management Agency coordinating disaster response and recovery efforts.',
    website: 'https://fema.gov',
    logo: 'https://images.pexels.com/photos/8815176/pexels-photo-8815176.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1'
  },
  {
    id: '3',
    name: 'Disaster Relief Corp',
    type: 'Corporate',
    description: 'Corporate foundation focused on rapid disaster response and community rebuilding.',
    website: 'https://disasterrelief.com',
    logo: 'https://images.pexels.com/photos/3862627/pexels-photo-3862627.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1'
  }
];

export const mockSafetyGuides: SafetyGuide[] = [
  {
    id: '1',
    title: 'Flood Safety Guidelines',
    category: 'Flood',
    content: 'Essential safety measures during flooding situations...',
    lastUpdated: new Date('2024-01-01')
  },
  {
    id: '2',
    title: 'Earthquake Preparedness',
    category: 'Earthquake',
    content: 'How to prepare for and respond to earthquakes...',
    lastUpdated: new Date('2024-01-01')
  }
];