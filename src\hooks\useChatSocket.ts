import { useState, useEffect, useCallback, useRef } from 'react';
import { ChatMessage, ChatUser, ChatRoom, MessageReaction } from '../types/chat';

interface UseChatSocketReturn {
  messages: ChatMessage[];
  users: ChatUser[];
  rooms: ChatRoom[];
  isConnected: boolean;
  sendMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => Promise<void>;
  joinRoom: (roomId: string) => void;
  leaveRoom: (roomId: string) => void;
  markAsRead: (messageId: string) => void;
  editMessage: (messageId: string, newContent: string) => void;
  deleteMessage: (messageId: string) => void;
  addReaction: (messageId: string, emoji: string) => void;
  removeReaction: (messageId: string, emoji: string) => void;
  typingUsers: string[];
  startTyping: () => void;
  stopTyping: () => void;
}

export const useChatSocket = (userId: string, roomId: string): UseChatSocketReturn => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [users, setUsers] = useState<ChatUser[]>([]);
  const [rooms, setRooms] = useState<ChatRoom[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const messageQueueRef = useRef<any[]>([]);

  // Mock data for demonstration
  const mockUsers: ChatUser[] = [
    {
      id: '1',
      name: 'John Anderson',
      avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1',
      isOnline: true,
      role: 'admin',
      lastSeen: new Date(),
      isTyping: false
    },
    {
      id: '2',
      name: 'Sarah Chen',
      avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1',
      isOnline: true,
      role: 'moderator',
      lastSeen: new Date(),
      isTyping: false
    },
    {
      id: '3',
      name: 'Mike Rodriguez',
      avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1',
      isOnline: false,
      role: 'user',
      lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      isTyping: false
    },
    {
      id: '4',
      name: 'Emily Watson',
      avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1',
      isOnline: true,
      role: 'user',
      lastSeen: new Date(),
      isTyping: false
    }
  ];

  const mockRooms: ChatRoom[] = [
    {
      id: 'general',
      name: 'General Discussion',
      description: 'General community chat',
      memberCount: 1247,
      isPrivate: false
    },
    {
      id: 'emergency',
      name: 'Emergency Alerts',
      description: 'Real-time emergency notifications',
      memberCount: 892,
      isPrivate: false
    },
    {
      id: 'volunteers',
      name: 'Volunteer Coordination',
      description: 'Coordinate volunteer efforts',
      memberCount: 456,
      isPrivate: false
    }
  ];

  const mockMessages: ChatMessage[] = [
    {
      id: '1',
      content: 'Welcome to the DisasterWatch community chat! This is where we coordinate disaster response efforts.',
      senderId: '1',
      senderName: 'John Anderson',
      senderAvatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1',
      roomId: 'general',
      type: 'text',
      timestamp: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
      read: true,
      reactions: [
        { emoji: '👍', userId: '2', userName: 'Sarah Chen' },
        { emoji: '❤️', userId: '4', userName: 'Emily Watson' }
      ]
    },
    {
      id: '2',
      content: 'Thanks for setting this up! This will be really helpful for coordinating our response efforts.',
      senderId: '2',
      senderName: 'Sarah Chen',
      senderAvatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1',
      roomId: 'general',
      type: 'text',
      timestamp: new Date(Date.now() - 8 * 60 * 1000), // 8 minutes ago
      read: true,
      reactions: [
        { emoji: '👍', userId: '1', userName: 'John Anderson' }
      ]
    },
    {
      id: '3',
      content: 'I have some emergency supplies available if anyone needs them. Located in downtown area.',
      senderId: '4',
      senderName: 'Emily Watson',
      senderAvatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1',
      roomId: 'general',
      type: 'text',
      timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
      read: false,
      reactions: [
        { emoji: '🙏', userId: '2', userName: 'Sarah Chen' },
        { emoji: '❤️', userId: '1', userName: 'John Anderson' }
      ]
    }
  ];

  // Initialize mock data
  useEffect(() => {
    setUsers(mockUsers);
    setRooms(mockRooms);
    setMessages(mockMessages);
  }, []);

  // WebSocket connection management
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      // In a real implementation, this would connect to your WebSocket server
      // For demo purposes, we'll simulate a connection
      setIsConnected(true);
      
      // Process any queued messages
      if (messageQueueRef.current.length > 0) {
        messageQueueRef.current.forEach(message => {
          // Process queued messages
        });
        messageQueueRef.current = [];
      }

      // Simulate periodic connection checks
      const interval = setInterval(() => {
        if (Math.random() > 0.95) { // 5% chance of disconnection for demo
          setIsConnected(false);
          clearInterval(interval);
          // Attempt reconnection
          setTimeout(() => connect(), 2000);
        }
      }, 10000);

    } catch (error) {
      console.error('WebSocket connection failed:', error);
      setIsConnected(false);
      
      // Retry connection
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      reconnectTimeoutRef.current = setTimeout(() => connect(), 3000);
    }
  }, []);

  // Initialize connection
  useEffect(() => {
    if (userId) {
      connect();
    }

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [userId, connect]);

  // Send message
  const sendMessage = useCallback(async (message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
    const newMessage: ChatMessage = {
      ...message,
      id: Date.now().toString(),
      timestamp: new Date()
    };

    if (isConnected) {
      // In a real implementation, send via WebSocket
      setMessages(prev => [...prev, newMessage]);
      
      // Simulate message delivery delay
      setTimeout(() => {
        setMessages(prev => 
          prev.map(msg => 
            msg.id === newMessage.id ? { ...msg, read: true } : msg
          )
        );
      }, 1000);
    } else {
      // Queue message for later delivery
      messageQueueRef.current.push(newMessage);
      setMessages(prev => [...prev, { ...newMessage, read: false }]);
    }
  }, [isConnected]);

  // Join room
  const joinRoom = useCallback((roomId: string) => {
    if (isConnected) {
      // In a real implementation, send join room message via WebSocket
      console.log(`Joining room: ${roomId}`);
    }
  }, [isConnected]);

  // Leave room
  const leaveRoom = useCallback((roomId: string) => {
    if (isConnected) {
      // In a real implementation, send leave room message via WebSocket
      console.log(`Leaving room: ${roomId}`);
    }
  }, [isConnected]);

  // Mark message as read
  const markAsRead = useCallback((messageId: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId ? { ...msg, read: true } : msg
      )
    );
  }, []);

  // Edit message
  const editMessage = useCallback((messageId: string, newContent: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId ? { ...msg, content: newContent, edited: true } : msg
      )
    );
  }, []);

  // Delete message
  const deleteMessage = useCallback((messageId: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== messageId));
  }, []);

  // Add reaction
  const addReaction = useCallback((messageId: string, emoji: string) => {
    const currentUser = users.find(u => u.id === userId);
    if (!currentUser) return;

    setMessages(prev =>
      prev.map(msg => {
        if (msg.id === messageId) {
          const existingReaction = msg.reactions.find(r => r.emoji === emoji && r.userId === userId);
          if (!existingReaction) {
            return {
              ...msg,
              reactions: [...msg.reactions, { emoji, userId, userName: currentUser.name }]
            };
          }
        }
        return msg;
      })
    );
  }, [userId, users]);

  // Remove reaction
  const removeReaction = useCallback((messageId: string, emoji: string) => {
    setMessages(prev =>
      prev.map(msg => {
        if (msg.id === messageId) {
          return {
            ...msg,
            reactions: msg.reactions.filter(r => !(r.emoji === emoji && r.userId === userId))
          };
        }
        return msg;
      })
    );
  }, [userId]);

  // Start typing
  const startTyping = useCallback(() => {
    if (isConnected) {
      // In a real implementation, send typing indicator via WebSocket
      console.log('User started typing');
    }
  }, [isConnected]);

  // Stop typing
  const stopTyping = useCallback(() => {
    if (isConnected) {
      // In a real implementation, send stop typing indicator via WebSocket
      console.log('User stopped typing');
    }
  }, [isConnected]);

  return {
    messages: messages.filter(msg => msg.roomId === roomId),
    users,
    rooms,
    isConnected,
    sendMessage,
    joinRoom,
    leaveRoom,
    markAsRead,
    editMessage,
    deleteMessage,
    addReaction,
    removeReaction,
    typingUsers,
    startTyping,
    stopTyping
  };
};