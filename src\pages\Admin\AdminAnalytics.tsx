import React, { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import { 
  TrendingUp, 
  Users, 
  Eye, 
  Clock, 
  Download, 
  Calendar,
  Globe,
  Smartphone,
  Monitor,
  Tablet,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { useAdmin } from '../../context/AdminContext';

const AdminAnalytics: React.FC = () => {
  const { analytics } = useAdmin();
  const [dateRange, setDateRange] = useState('30');
  const [selectedMetric, setSelectedMetric] = useState('visitors');

  // Mock time series data
  const timeSeriesData = [
    { date: 'Jan 1', visitors: 1200, pageViews: 3400, conversions: 45 },
    { date: 'Jan 2', visitors: 1350, pageViews: 3800, conversions: 52 },
    { date: 'Jan 3', visitors: 1100, pageViews: 3200, conversions: 38 },
    { date: 'Jan 4', visitors: 1450, pageViews: 4100, conversions: 61 },
    { date: 'Jan 5', visitors: 1600, pageViews: 4500, conversions: 68 },
    { date: 'Jan 6', visitors: 1380, pageViews: 3900, conversions: 55 },
    { date: 'Jan 7', visitors: 1520, pageViews: 4200, conversions: 63 }
  ];

  const conversionFunnelData = [
    { stage: 'Visitors', count: 12450, percentage: 100 },
    { stage: 'Page Views', count: 8920, percentage: 71.6 },
    { stage: 'Engagement', count: 4560, percentage: 36.6 },
    { stage: 'Sign-ups', count: 892, percentage: 7.2 },
    { stage: 'Reports', count: 247, percentage: 2.0 }
  ];

  const topPagesData = [
    { page: '/', views: 15420, bounce: 32.4 },
    { page: '/reports', views: 8930, bounce: 28.1 },
    { page: '/report/new', views: 5670, bounce: 15.2 },
    { page: '/resources', views: 4320, bounce: 45.6 },
    { page: '/partners', views: 2890, bounce: 38.9 }
  ];

  const exportData = () => {
    const csvContent = "data:text/csv;charset=utf-8," 
      + "Metric,Value\n"
      + `Total Visitors,${analytics?.visitors.total}\n`
      + `Page Views,${analytics?.engagement.pageViews}\n`
      + `Conversion Rate,${analytics?.conversions.rate}%\n`
      + `Avg Session Duration,${analytics?.engagement.avgSessionDuration}s`;
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "analytics_data.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getMetricIcon = (metric: string) => {
    switch (metric) {
      case 'visitors': return Users;
      case 'pageViews': return Eye;
      case 'conversions': return TrendingUp;
      default: return Clock;
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  };

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Analytics Dashboard</h1>
          <p className="text-gray-600">Detailed insights into your platform performance</p>
        </div>
        <div className="mt-4 md:mt-0 flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Calendar size={20} className="text-gray-500" />
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="border border-gray-200 rounded-xl px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="7">Last 7 Days</option>
              <option value="30">Last 30 Days</option>
              <option value="90">Last 3 Months</option>
              <option value="365">Last Year</option>
            </select>
          </div>
          <button
            onClick={exportData}
            className="bg-blue-600 text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition-colors flex items-center"
          >
            <Download size={16} className="mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          {
            title: 'Total Visitors',
            value: analytics?.visitors.total || 0,
            change: analytics?.visitors.trend || 0,
            icon: Users,
            color: 'bg-blue-50 text-blue-600'
          },
          {
            title: 'Page Views',
            value: analytics?.engagement.pageViews || 0,
            change: analytics?.engagement.trend || 0,
            icon: Eye,
            color: 'bg-green-50 text-green-600'
          },
          {
            title: 'Conversion Rate',
            value: `${analytics?.conversions.rate || 0}%`,
            change: analytics?.conversions.trend || 0,
            icon: TrendingUp,
            color: 'bg-purple-50 text-purple-600'
          },
          {
            title: 'Avg. Session',
            value: `${Math.floor((analytics?.engagement.avgSessionDuration || 0) / 60)}:${String((analytics?.engagement.avgSessionDuration || 0) % 60).padStart(2, '0')}`,
            change: 8.2,
            icon: Clock,
            color: 'bg-orange-50 text-orange-600'
          }
        ].map((metric, index) => (
          <div key={index} className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-xl ${metric.color}`}>
                <metric.icon size={24} />
              </div>
              <div className={`flex items-center text-sm font-medium ${
                metric.change >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {metric.change >= 0 ? <ArrowUp size={16} /> : <ArrowDown size={16} />}
                <span className="ml-1">{Math.abs(metric.change)}%</span>
              </div>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-1">{metric.value}</h3>
            <p className="text-gray-600 text-sm">{metric.title}</p>
          </div>
        ))}
      </div>

      {/* Time Series Chart */}
      <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Traffic Trends</h2>
          <div className="flex items-center space-x-2">
            {['visitors', 'pageViews', 'conversions'].map((metric) => {
              const Icon = getMetricIcon(metric);
              return (
                <button
                  key={metric}
                  onClick={() => setSelectedMetric(metric)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedMetric === metric
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <Icon size={16} />
                  <span className="capitalize">{metric}</span>
                </button>
              );
            })}
          </div>
        </div>
        <ResponsiveContainer width="100%" height={400}>
          <AreaChart data={timeSeriesData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Area 
              type="monotone" 
              dataKey={selectedMetric} 
              stroke="#4A90E2" 
              fill="#4A90E2" 
              fillOpacity={0.1}
              strokeWidth={2}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Traffic Sources */}
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Traffic Sources</h2>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={analytics?.traffic.sources || []}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, value }) => `${name} ${value}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {(analytics?.traffic.sources || []).map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Device Breakdown */}
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Device Usage</h2>
          <div className="space-y-4">
            {(analytics?.traffic.devices || []).map((device, index) => {
              const icons = { Desktop: Monitor, Mobile: Smartphone, Tablet: Tablet };
              const Icon = icons[device.name as keyof typeof icons] || Monitor;
              return (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gray-100 rounded-lg">
                      <Icon size={20} className="text-gray-600" />
                    </div>
                    <span className="font-medium text-gray-900">{device.name}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${device.value}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium text-gray-600 w-12 text-right">
                      {device.value}%
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Conversion Funnel */}
      <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Conversion Funnel</h2>
        <div className="space-y-4">
          {conversionFunnelData.map((stage, index) => (
            <div key={index} className="relative">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-gray-900">{stage.stage}</span>
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">{stage.count.toLocaleString()}</span>
                  <span className="text-sm font-medium text-gray-900">{stage.percentage}%</span>
                </div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className="bg-gradient-to-r from-blue-500 to-blue-600 h-3 rounded-full transition-all duration-500" 
                  style={{ width: `${stage.percentage}%` }}
                ></div>
              </div>
              {index < conversionFunnelData.length - 1 && (
                <div className="absolute right-0 top-8 text-xs text-red-600 font-medium">
                  -{((conversionFunnelData[index].count - conversionFunnelData[index + 1].count) / conversionFunnelData[index].count * 100).toFixed(1)}%
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Top Pages */}
      <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Top Pages</h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 text-sm font-medium text-gray-700">Page</th>
                <th className="text-right py-3 text-sm font-medium text-gray-700">Views</th>
                <th className="text-right py-3 text-sm font-medium text-gray-700">Bounce Rate</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100">
              {topPagesData.map((page, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="py-3 text-sm text-gray-900">{page.page}</td>
                  <td className="py-3 text-sm text-gray-900 text-right">{page.views.toLocaleString()}</td>
                  <td className="py-3 text-sm text-right">
                    <span className={`font-medium ${
                      page.bounce < 30 ? 'text-green-600' : 
                      page.bounce < 40 ? 'text-orange-600' : 'text-red-600'
                    }`}>
                      {page.bounce}%
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Geographic Data */}
      <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Top Visitor Locations</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {(analytics?.traffic.locations || []).map((location, index) => (
            <div key={index} className="text-center p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">
              <div className="flex items-center justify-center mb-2">
                <Globe size={20} className="text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-1">{location.country}</h3>
              <p className="text-2xl font-bold text-blue-600 mb-1">{formatNumber(location.visitors)}</p>
              <p className="text-xs text-gray-600">visitors</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AdminAnalytics;