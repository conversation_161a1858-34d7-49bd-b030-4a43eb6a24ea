import React, { useState } from 'react';
import { 
  FileText, 
  Edit, 
  Eye, 
  Save, 
  Clock, 
  Globe, 
  Image, 
  Type, 
  Layout,
  Calendar,
  CheckCircle,
  AlertTriangle,
  Plus,
  Search,
  Filter
} from 'lucide-react';
import { useAdmin } from '../../context/AdminContext';
import { format } from 'date-fns';

const ContentManagement: React.FC = () => {
  const { contentSections, updateContent, publishContent, scheduleContent, isLoading } = useAdmin();
  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const [editingContent, setEditingContent] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [scheduleDate, setScheduleDate] = useState('');

  const filteredSections = contentSections.filter(section => {
    const matchesSearch = section.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || 
      (filterStatus === 'published' && section.isPublished) ||
      (filterStatus === 'draft' && !section.isPublished) ||
      (filterStatus === 'scheduled' && section.scheduledAt);
    return matchesSearch && matchesFilter;
  });

  const handleEdit = (section: any) => {
    setSelectedSection(section.id);
    setEditingContent({ ...section.content });
  };

  const handleSave = async () => {
    if (selectedSection && editingContent) {
      await updateContent(selectedSection, editingContent);
      setSelectedSection(null);
      setEditingContent(null);
    }
  };

  const handlePublish = async (sectionId: string) => {
    await publishContent(sectionId);
  };

  const handleSchedule = async () => {
    if (selectedSection && scheduleDate) {
      await scheduleContent(selectedSection, new Date(scheduleDate));
      setShowScheduleModal(false);
      setScheduleDate('');
    }
  };

  const getStatusBadge = (section: any) => {
    if (section.scheduledAt) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          <Clock size={12} className="mr-1" />
          Scheduled
        </span>
      );
    }
    if (section.isPublished) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <CheckCircle size={12} className="mr-1" />
          Published
        </span>
      );
    }
    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
        <AlertTriangle size={12} className="mr-1" />
        Draft
      </span>
    );
  };

  const renderContentEditor = () => {
    if (!selectedSection || !editingContent) return null;

    const section = contentSections.find(s => s.id === selectedSection);
    if (!section) return null;

    return (
      <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Edit {section.title}</h2>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => {
                setSelectedSection(null);
                setEditingContent(null);
              }}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center disabled:opacity-50"
            >
              <Save size={16} className="mr-2" />
              Save Changes
            </button>
          </div>
        </div>

        {section.type === 'hero' && (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Type size={16} className="inline mr-2" />
                Headline
              </label>
              <input
                type="text"
                value={editingContent.headline || ''}
                onChange={(e) => setEditingContent({ ...editingContent, headline: e.target.value })}
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter hero headline"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Type size={16} className="inline mr-2" />
                Subheadline
              </label>
              <textarea
                value={editingContent.subheadline || ''}
                onChange={(e) => setEditingContent({ ...editingContent, subheadline: e.target.value })}
                rows={3}
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter hero subheadline"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Type size={16} className="inline mr-2" />
                CTA Button Text
              </label>
              <input
                type="text"
                value={editingContent.ctaText || ''}
                onChange={(e) => setEditingContent({ ...editingContent, ctaText: e.target.value })}
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter button text"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Image size={16} className="inline mr-2" />
                Background Image URL
              </label>
              <input
                type="url"
                value={editingContent.backgroundImage || ''}
                onChange={(e) => setEditingContent({ ...editingContent, backgroundImage: e.target.value })}
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter image URL"
              />
            </div>
          </div>
        )}

        {section.type === 'features' && (
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Type size={16} className="inline mr-2" />
                Section Title
              </label>
              <input
                type="text"
                value={editingContent.title || ''}
                onChange={(e) => setEditingContent({ ...editingContent, title: e.target.value })}
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter section title"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Type size={16} className="inline mr-2" />
                Subtitle
              </label>
              <textarea
                value={editingContent.subtitle || ''}
                onChange={(e) => setEditingContent({ ...editingContent, subtitle: e.target.value })}
                rows={2}
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter section subtitle"
              />
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Content Management</h1>
          <p className="text-gray-600">Manage and edit your website content</p>
        </div>
        <button className="mt-4 md:mt-0 bg-blue-600 text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition-colors flex items-center">
          <Plus size={16} className="mr-2" />
          Add New Section
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search content sections..."
                className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          <div className="md:w-48">
            <div className="relative">
              <Filter size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
              >
                <option value="all">All Status</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
                <option value="scheduled">Scheduled</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Content Editor */}
      {selectedSection && renderContentEditor()}

      {/* Content Sections List */}
      <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900">Content Sections</h2>
        </div>
        <div className="divide-y divide-gray-100">
          {filteredSections.map((section) => (
            <div key={section.id} className="p-6 hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <Layout size={20} className="text-gray-400" />
                    <h3 className="text-lg font-semibold text-gray-900">{section.title}</h3>
                    {getStatusBadge(section)}
                  </div>
                  <div className="flex items-center space-x-6 text-sm text-gray-600">
                    <span>Type: {section.type}</span>
                    <span>Version: {section.version}</span>
                    <span>Modified: {format(section.lastModified, 'MMM d, yyyy HH:mm')}</span>
                    <span>By: {section.modifiedBy}</span>
                  </div>
                  {section.scheduledAt && (
                    <div className="mt-2 text-sm text-blue-600">
                      Scheduled for: {format(section.scheduledAt, 'MMM d, yyyy HH:mm')}
                    </div>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleEdit(section)}
                    className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="Edit"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                    title="Preview"
                  >
                    <Eye size={16} />
                  </button>
                  {!section.isPublished && (
                    <button
                      onClick={() => handlePublish(section.id)}
                      className="px-3 py-1 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                    >
                      Publish
                    </button>
                  )}
                  <button
                    onClick={() => {
                      setSelectedSection(section.id);
                      setShowScheduleModal(true);
                    }}
                    className="px-3 py-1 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                  >
                    Schedule
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Schedule Modal */}
      {showScheduleModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Schedule Publication</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Publication Date & Time
                </label>
                <input
                  type="datetime-local"
                  value={scheduleDate}
                  onChange={(e) => setScheduleDate(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div className="flex space-x-3 pt-4">
                <button
                  onClick={handleSchedule}
                  disabled={!scheduleDate}
                  className="flex-1 bg-blue-600 text-white py-3 rounded-xl hover:bg-blue-700 transition-colors disabled:opacity-50"
                >
                  Schedule
                </button>
                <button
                  onClick={() => {
                    setShowScheduleModal(false);
                    setScheduleDate('');
                  }}
                  className="flex-1 border border-gray-300 text-gray-700 py-3 rounded-xl hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContentManagement;