import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  Users, 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Download, 
  Filter,
  Search,
  Eye,
  Lock,
  Unlock,
  UserCheck,
  UserX,
  FileText,
  Calendar,
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  Globe,
  Smartphone,
  Monitor,
  RefreshCw,
  Database,
  Server,
  Wifi,
  HardDrive
} from 'lucide-react';
import { format, subDays, subHours, subMinutes } from 'date-fns';
import { useAuth } from '../../context/AuthContext';

interface AdminUser {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'editor' | 'viewer';
  status: 'active' | 'inactive' | 'suspended';
  permissions: string[];
  lastLogin: Date;
  loginCount: number;
  createdAt: Date;
  lastActivity: Date;
  ipAddress: string;
  userAgent: string;
  securityFlags: string[];
}

interface ContentActivity {
  id: string;
  userId: string;
  userName: string;
  action: 'create' | 'update' | 'delete' | 'publish' | 'unpublish';
  contentType: string;
  contentId: string;
  contentTitle: string;
  timestamp: Date;
  changes: string[];
  status: 'success' | 'failed' | 'pending';
  ipAddress: string;
  userAgent: string;
}

interface SecurityEvent {
  id: string;
  type: 'login_attempt' | 'permission_change' | 'security_incident' | 'auth_verification';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  userName?: string;
  description: string;
  timestamp: Date;
  ipAddress: string;
  userAgent: string;
  resolved: boolean;
  details: Record<string, any>;
}

interface SystemMetrics {
  totalUsers: number;
  activeUsers: number;
  adminUsers: number;
  editorUsers: number;
  suspendedUsers: number;
  totalLogins: number;
  failedLogins: number;
  securityIncidents: number;
  contentModifications: number;
  systemUptime: string;
  lastBackup: Date;
  databaseSize: string;
  storageUsed: string;
}

const SystemReport: React.FC = () => {
  const { user, isAdmin } = useAuth();
  const [reportData, setReportData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'users' | 'content' | 'security' | 'system'>('overview');
  const [dateRange, setDateRange] = useState('7');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  // Mock data generation
  useEffect(() => {
    const generateMockData = () => {
      const now = new Date();
      
      // Generate mock admin users
      const adminUsers: AdminUser[] = [
        {
          id: 'admin-1',
          name: 'Sarah Chen',
          email: '<EMAIL>',
          role: 'admin',
          status: 'active',
          permissions: ['admin.dashboard.view', 'admin.users.manage', 'admin.content.manage', 'admin.analytics.view', 'admin.settings.manage'],
          lastLogin: subMinutes(now, 15),
          loginCount: 247,
          createdAt: subDays(now, 180),
          lastActivity: subMinutes(now, 5),
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          securityFlags: []
        },
        {
          id: 'editor-1',
          name: 'Mike Rodriguez',
          email: '<EMAIL>',
          role: 'editor',
          status: 'active',
          permissions: ['admin.content.manage', 'admin.analytics.view'],
          lastLogin: subHours(now, 2),
          loginCount: 156,
          createdAt: subDays(now, 120),
          lastActivity: subMinutes(now, 45),
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          securityFlags: []
        },
        {
          id: 'editor-2',
          name: 'Emily Watson',
          email: '<EMAIL>',
          role: 'editor',
          status: 'inactive',
          permissions: ['admin.content.manage'],
          lastLogin: subDays(now, 5),
          loginCount: 89,
          createdAt: subDays(now, 90),
          lastActivity: subDays(now, 5),
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15',
          securityFlags: ['inactive_account']
        },
        {
          id: 'admin-2',
          name: 'David Kim',
          email: '<EMAIL>',
          role: 'admin',
          status: 'suspended',
          permissions: [],
          lastLogin: subDays(now, 10),
          loginCount: 45,
          createdAt: subDays(now, 60),
          lastActivity: subDays(now, 10),
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
          securityFlags: ['suspicious_activity', 'multiple_failed_logins']
        }
      ];

      // Generate mock content activities
      const contentActivities: ContentActivity[] = [
        {
          id: 'content-1',
          userId: 'admin-1',
          userName: 'Sarah Chen',
          action: 'update',
          contentType: 'hero_section',
          contentId: 'hero-1',
          contentTitle: 'Homepage Hero Section',
          timestamp: subMinutes(now, 30),
          changes: ['Updated headline text', 'Changed CTA button color'],
          status: 'success',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        {
          id: 'content-2',
          userId: 'editor-1',
          userName: 'Mike Rodriguez',
          action: 'publish',
          contentType: 'blog_post',
          contentId: 'blog-15',
          contentTitle: 'Emergency Preparedness Guide 2024',
          timestamp: subHours(now, 1),
          changes: ['Published content', 'Set publication date'],
          status: 'success',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        },
        {
          id: 'content-3',
          userId: 'editor-1',
          userName: 'Mike Rodriguez',
          action: 'create',
          contentType: 'safety_guide',
          contentId: 'guide-8',
          contentTitle: 'Flood Safety Procedures',
          timestamp: subHours(now, 3),
          changes: ['Created new guide', 'Added content sections', 'Uploaded images'],
          status: 'success',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        },
        {
          id: 'content-4',
          userId: 'admin-1',
          userName: 'Sarah Chen',
          action: 'delete',
          contentType: 'user_report',
          contentId: 'report-247',
          contentTitle: 'Spam Report - Fake Disaster',
          timestamp: subHours(now, 6),
          changes: ['Deleted spam content', 'Banned user account'],
          status: 'success',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      ];

      // Generate mock security events
      const securityEvents: SecurityEvent[] = [
        {
          id: 'sec-1',
          type: 'login_attempt',
          severity: 'medium',
          userId: 'admin-2',
          userName: 'David Kim',
          description: 'Multiple failed login attempts detected',
          timestamp: subHours(now, 2),
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
          resolved: true,
          details: {
            attemptCount: 5,
            timeWindow: '15 minutes',
            action: 'Account temporarily locked'
          }
        },
        {
          id: 'sec-2',
          type: 'permission_change',
          severity: 'high',
          userId: 'admin-1',
          userName: 'Sarah Chen',
          description: 'Admin permissions revoked for user David Kim',
          timestamp: subHours(now, 4),
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          resolved: true,
          details: {
            targetUser: 'David Kim',
            permissionsRemoved: ['admin.users.manage', 'admin.settings.manage'],
            reason: 'Suspicious activity detected'
          }
        },
        {
          id: 'sec-3',
          type: 'security_incident',
          severity: 'critical',
          description: 'Unusual access pattern detected from foreign IP',
          timestamp: subHours(now, 8),
          ipAddress: '************',
          userAgent: 'curl/7.68.0',
          resolved: false,
          details: {
            country: 'Unknown',
            accessAttempts: 15,
            endpoints: ['/admin/users', '/admin/settings', '/admin/database'],
            blocked: true
          }
        },
        {
          id: 'sec-4',
          type: 'auth_verification',
          severity: 'low',
          userId: 'editor-1',
          userName: 'Mike Rodriguez',
          description: 'Two-factor authentication enabled',
          timestamp: subDays(now, 1),
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          resolved: true,
          details: {
            method: 'TOTP',
            backupCodes: 'generated'
          }
        }
      ];

      // Generate system metrics
      const systemMetrics: SystemMetrics = {
        totalUsers: 12450,
        activeUsers: 8920,
        adminUsers: 3,
        editorUsers: 8,
        suspendedUsers: 2,
        totalLogins: 45680,
        failedLogins: 234,
        securityIncidents: 12,
        contentModifications: 1247,
        systemUptime: '99.97%',
        lastBackup: subHours(now, 6),
        databaseSize: '2.4 GB',
        storageUsed: '15.7 GB'
      };

      setReportData({
        adminUsers,
        contentActivities,
        securityEvents,
        systemMetrics,
        generatedAt: now
      });
      setLoading(false);
    };

    generateMockData();
  }, [dateRange]);

  const exportReport = () => {
    const reportContent = {
      generatedAt: new Date().toISOString(),
      generatedBy: user?.name,
      reportType: 'System Administrator Comprehensive Report',
      dateRange: `Last ${dateRange} days`,
      data: reportData
    };

    const blob = new Blob([JSON.stringify(reportContent, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `system-report-${format(new Date(), 'yyyy-MM-dd-HHmm')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'inactive': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'suspended': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (!isAdmin()) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Shield size={48} className="mx-auto text-red-500 mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600">System Administrator privileges required.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Generating comprehensive system report...</p>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: 'System Overview', icon: BarChart3 },
    { id: 'users', label: 'User Management', icon: Users },
    { id: 'content', label: 'Content Activities', icon: FileText },
    { id: 'security', label: 'Security Audit', icon: Shield },
    { id: 'system', label: 'System Health', icon: Server }
  ];

  return (
    <div className="p-6 space-y-8 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-3 bg-red-100 text-red-600 rounded-xl">
                <Shield size={24} />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">System Administrator Report</h1>
                <p className="text-gray-600">Comprehensive security and operational audit</p>
              </div>
            </div>
            <div className="flex items-center space-x-6 text-sm text-gray-600">
              <span>Generated: {format(reportData.generatedAt, 'PPpp')}</span>
              <span>Report ID: SYS-{format(reportData.generatedAt, 'yyyyMMdd-HHmm')}</span>
              <span>Classification: CONFIDENTIAL</span>
            </div>
          </div>
          
          <div className="mt-6 lg:mt-0 flex items-center space-x-4">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="border border-gray-200 rounded-xl px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="1">Last 24 Hours</option>
              <option value="7">Last 7 Days</option>
              <option value="30">Last 30 Days</option>
              <option value="90">Last 90 Days</option>
            </select>
            <button
              onClick={exportReport}
              className="bg-blue-600 text-white px-6 py-2 rounded-xl hover:bg-blue-700 transition-colors flex items-center"
            >
              <Download size={16} className="mr-2" />
              Export Report
            </button>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-2">
        <div className="flex space-x-1 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setSelectedTab(tab.id as any)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all whitespace-nowrap ${
                selectedTab === tab.id
                  ? 'bg-blue-100 text-blue-700 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <tab.icon size={16} />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content based on selected tab */}
      {selectedTab === 'overview' && (
        <div className="space-y-8">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-blue-100 text-blue-600 rounded-xl">
                  <Users size={24} />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">{reportData.systemMetrics.totalUsers.toLocaleString()}</div>
                  <div className="text-sm text-gray-600">Total Users</div>
                </div>
              </div>
              <div className="text-sm text-gray-600">
                {reportData.systemMetrics.activeUsers.toLocaleString()} active • {reportData.systemMetrics.adminUsers} admins
              </div>
            </div>

            <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-green-100 text-green-600 rounded-xl">
                  <CheckCircle size={24} />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">{reportData.systemMetrics.systemUptime}</div>
                  <div className="text-sm text-gray-600">System Uptime</div>
                </div>
              </div>
              <div className="text-sm text-gray-600">
                Last backup: {format(reportData.systemMetrics.lastBackup, 'PPp')}
              </div>
            </div>

            <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-orange-100 text-orange-600 rounded-xl">
                  <AlertTriangle size={24} />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">{reportData.systemMetrics.securityIncidents}</div>
                  <div className="text-sm text-gray-600">Security Incidents</div>
                </div>
              </div>
              <div className="text-sm text-gray-600">
                {reportData.systemMetrics.failedLogins} failed logins
              </div>
            </div>

            <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-purple-100 text-purple-600 rounded-xl">
                  <Activity size={24} />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">{reportData.systemMetrics.contentModifications.toLocaleString()}</div>
                  <div className="text-sm text-gray-600">Content Changes</div>
                </div>
              </div>
              <div className="text-sm text-gray-600">
                Database: {reportData.systemMetrics.databaseSize}
              </div>
            </div>
          </div>

          {/* Recent Critical Events */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Critical Security Events</h2>
            <div className="space-y-4">
              {reportData.securityEvents
                .filter((event: SecurityEvent) => event.severity === 'critical' || event.severity === 'high')
                .slice(0, 5)
                .map((event: SecurityEvent) => (
                <div key={event.id} className="flex items-start space-x-4 p-4 bg-red-50 border border-red-200 rounded-xl">
                  <div className="p-2 bg-red-100 text-red-600 rounded-lg">
                    <AlertTriangle size={16} />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium text-gray-900">{event.description}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getSeverityColor(event.severity)}`}>
                        {event.severity.toUpperCase()}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>Time: {format(event.timestamp, 'PPpp')}</p>
                      <p>IP: {event.ipAddress}</p>
                      {event.userName && <p>User: {event.userName}</p>}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {selectedTab === 'users' && (
        <div className="space-y-6">
          {/* User Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">{reportData.systemMetrics.adminUsers}</div>
              <div className="text-sm text-gray-600">Admin Users</div>
            </div>
            <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">{reportData.systemMetrics.editorUsers}</div>
              <div className="text-sm text-gray-600">Editor Users</div>
            </div>
            <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm text-center">
              <div className="text-3xl font-bold text-yellow-600 mb-2">{reportData.adminUsers.filter((u: AdminUser) => u.status === 'inactive').length}</div>
              <div className="text-sm text-gray-600">Inactive Users</div>
            </div>
            <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm text-center">
              <div className="text-3xl font-bold text-red-600 mb-2">{reportData.systemMetrics.suspendedUsers}</div>
              <div className="text-sm text-gray-600">Suspended Users</div>
            </div>
          </div>

          {/* Admin Users Table */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-900">Admin User Accounts</h2>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">User</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Role</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Status</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Last Login</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Login Count</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Permissions</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Security Flags</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {reportData.adminUsers.map((user: AdminUser) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div>
                          <div className="font-medium text-gray-900">{user.name}</div>
                          <div className="text-sm text-gray-600">{user.email}</div>
                          <div className="text-xs text-gray-500">ID: {user.id}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          user.role === 'admin' ? 'bg-red-100 text-red-800' :
                          user.role === 'editor' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {user.role.toUpperCase()}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(user.status)}`}>
                          {user.status.toUpperCase()}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">{format(user.lastLogin, 'PPp')}</div>
                        <div className="text-xs text-gray-500">IP: {user.ipAddress}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm font-medium text-gray-900">{user.loginCount}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">{user.permissions.length} permissions</div>
                        <div className="text-xs text-gray-500">
                          {user.permissions.slice(0, 2).join(', ')}
                          {user.permissions.length > 2 && ` +${user.permissions.length - 2} more`}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        {user.securityFlags.length > 0 ? (
                          <div className="space-y-1">
                            {user.securityFlags.map((flag, index) => (
                              <span key={index} className="inline-block px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                                {flag}
                              </span>
                            ))}
                          </div>
                        ) : (
                          <span className="text-green-600 text-sm">✓ Clean</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {selectedTab === 'content' && (
        <div className="space-y-6">
          {/* Content Activity Summary */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            {['create', 'update', 'delete', 'publish', 'unpublish'].map((action) => {
              const count = reportData.contentActivities.filter((a: ContentActivity) => a.action === action).length;
              return (
                <div key={action} className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm text-center">
                  <div className="text-2xl font-bold text-gray-900 mb-2">{count}</div>
                  <div className="text-sm text-gray-600 capitalize">{action} Actions</div>
                </div>
              );
            })}
          </div>

          {/* Content Activities Table */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-900">Content Editor Activities</h2>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Timestamp</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">User</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Action</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Content</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Changes</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Status</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">IP Address</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {reportData.contentActivities.map((activity: ContentActivity) => (
                    <tr key={activity.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">{format(activity.timestamp, 'PPp')}</div>
                      </td>
                      <td className="px-6 py-4">
                        <div>
                          <div className="font-medium text-gray-900">{activity.userName}</div>
                          <div className="text-xs text-gray-500">ID: {activity.userId}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          activity.action === 'create' ? 'bg-green-100 text-green-800' :
                          activity.action === 'update' ? 'bg-blue-100 text-blue-800' :
                          activity.action === 'delete' ? 'bg-red-100 text-red-800' :
                          activity.action === 'publish' ? 'bg-purple-100 text-purple-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {activity.action.toUpperCase()}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div>
                          <div className="font-medium text-gray-900">{activity.contentTitle}</div>
                          <div className="text-sm text-gray-600">{activity.contentType}</div>
                          <div className="text-xs text-gray-500">ID: {activity.contentId}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">
                          {activity.changes.slice(0, 2).map((change, index) => (
                            <div key={index} className="text-xs text-gray-600">• {change}</div>
                          ))}
                          {activity.changes.length > 2 && (
                            <div className="text-xs text-gray-500">+{activity.changes.length - 2} more</div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          activity.status === 'success' ? 'bg-green-100 text-green-800' :
                          activity.status === 'failed' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {activity.status.toUpperCase()}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">{activity.ipAddress}</div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {selectedTab === 'security' && (
        <div className="space-y-6">
          {/* Security Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {['critical', 'high', 'medium', 'low'].map((severity) => {
              const count = reportData.securityEvents.filter((e: SecurityEvent) => e.severity === severity).length;
              return (
                <div key={severity} className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm text-center">
                  <div className={`text-2xl font-bold mb-2 ${
                    severity === 'critical' ? 'text-red-600' :
                    severity === 'high' ? 'text-orange-600' :
                    severity === 'medium' ? 'text-yellow-600' :
                    'text-green-600'
                  }`}>
                    {count}
                  </div>
                  <div className="text-sm text-gray-600 capitalize">{severity} Severity</div>
                </div>
              );
            })}
          </div>

          {/* Security Events Table */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-900">Security Audit Trail</h2>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Timestamp</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Event Type</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Severity</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Description</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">User</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">IP Address</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-gray-700">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {reportData.securityEvents.map((event: SecurityEvent) => (
                    <tr key={event.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">{format(event.timestamp, 'PPp')}</div>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          event.type === 'security_incident' ? 'bg-red-100 text-red-800' :
                          event.type === 'login_attempt' ? 'bg-yellow-100 text-yellow-800' :
                          event.type === 'permission_change' ? 'bg-blue-100 text-blue-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {event.type.replace('_', ' ').toUpperCase()}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getSeverityColor(event.severity)}`}>
                          {event.severity.toUpperCase()}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">{event.description}</div>
                        {Object.keys(event.details).length > 0 && (
                          <div className="text-xs text-gray-500 mt-1">
                            {Object.entries(event.details).slice(0, 2).map(([key, value]) => (
                              <div key={key}>{key}: {String(value)}</div>
                            ))}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4">
                        {event.userName ? (
                          <div>
                            <div className="text-sm text-gray-900">{event.userName}</div>
                            <div className="text-xs text-gray-500">ID: {event.userId}</div>
                          </div>
                        ) : (
                          <span className="text-gray-400">System</span>
                        )}
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">{event.ipAddress}</div>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          event.resolved ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {event.resolved ? 'RESOLVED' : 'OPEN'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {selectedTab === 'system' && (
        <div className="space-y-6">
          {/* System Health Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-green-100 text-green-600 rounded-xl">
                  <Server size={24} />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">{reportData.systemMetrics.systemUptime}</div>
                  <div className="text-sm text-gray-600">Uptime</div>
                </div>
              </div>
              <div className="text-sm text-green-600">System operational</div>
            </div>

            <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-blue-100 text-blue-600 rounded-xl">
                  <Database size={24} />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">{reportData.systemMetrics.databaseSize}</div>
                  <div className="text-sm text-gray-600">Database Size</div>
                </div>
              </div>
              <div className="text-sm text-gray-600">Last backup: {format(reportData.systemMetrics.lastBackup, 'PPp')}</div>
            </div>

            <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-purple-100 text-purple-600 rounded-xl">
                  <HardDrive size={24} />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">{reportData.systemMetrics.storageUsed}</div>
                  <div className="text-sm text-gray-600">Storage Used</div>
                </div>
              </div>
              <div className="text-sm text-gray-600">62% of total capacity</div>
            </div>

            <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-orange-100 text-orange-600 rounded-xl">
                  <Wifi size={24} />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">99.9%</div>
                  <div className="text-sm text-gray-600">Network Uptime</div>
                </div>
              </div>
              <div className="text-sm text-gray-600">All services operational</div>
            </div>
          </div>

          {/* System Status */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">System Component Status</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="font-medium text-gray-900">Core Services</h3>
                {[
                  { name: 'Web Server', status: 'operational', uptime: '99.97%' },
                  { name: 'Database', status: 'operational', uptime: '99.95%' },
                  { name: 'Authentication', status: 'operational', uptime: '100%' },
                  { name: 'File Storage', status: 'operational', uptime: '99.92%' }
                ].map((service, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                      <span className="font-medium text-gray-900">{service.name}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-green-600">{service.status}</div>
                      <div className="text-xs text-gray-500">{service.uptime}</div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="space-y-4">
                <h3 className="font-medium text-gray-900">Security Services</h3>
                {[
                  { name: 'Firewall', status: 'active', threats: '247 blocked' },
                  { name: 'SSL/TLS', status: 'valid', expires: 'Mar 2025' },
                  { name: 'Backup System', status: 'operational', last: '6 hours ago' },
                  { name: 'Monitoring', status: 'active', alerts: '0 active' }
                ].map((service, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                      <span className="font-medium text-gray-900">{service.name}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-green-600">{service.status}</div>
                      <div className="text-xs text-gray-500">{service.threats || service.expires || service.last || service.alerts}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Report Footer */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Shield size={20} className="text-red-600" />
            <span className="font-semibold text-gray-900">CONFIDENTIAL SYSTEM REPORT</span>
          </div>
          <div className="text-sm text-gray-600 space-y-1">
            <p>This report contains sensitive system information and should be handled according to security protocols.</p>
            <p>Generated by: {user?.name} ({user?.email}) • Report ID: SYS-{format(reportData.generatedAt, 'yyyyMMdd-HHmm')}</p>
            <p>Classification: CONFIDENTIAL • Distribution: Authorized Personnel Only</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemReport;