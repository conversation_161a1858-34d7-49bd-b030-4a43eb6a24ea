import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronRight, ChevronLeft, MapPin, Upload, AlertTriangle, Target } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import LocationPicker from '../components/Map/LocationPicker';
import LoginModal from '../components/Auth/LoginModal';

interface FormData {
  disasterType: 'Natural' | 'Non-Natural' | '';
  disasterDetail: string;
  customDisasterDetail: string;
  description: string;
  location: {
    lat: number;
    lng: number;
    address: string;
  } | null;
  photos: File[];
  assistanceNeeded: string[];
  assistanceDescription: string;
}

const CreateReport: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    disasterType: '',
    disasterDetail: '',
    customDisasterDetail: '',
    description: '',
    location: null,
    photos: [],
    assistanceNeeded: [],
    assistanceDescription: ''
  });

  const disasterOptions = {
    Natural: ['Earthquake', 'Flood', 'Wildfire', 'Tornado', 'Hurricane', 'Other'],
    'Non-Natural': ['Industrial Accident', 'Transportation Accident', 'Building Collapse', 'Other']
  };

  const assistanceTypes = [
    'Emergency Shelter',
    'Food & Water',
    'Medical Aid',
    'Evacuation Support',
    'Pet Care',
    'Transportation',
    'Cleanup Volunteers',
    'Construction Help',
    'Financial Support'
  ];

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    if (!user) {
      setShowLoginModal(true);
      return;
    }

    // Here you would normally submit to an API
    console.log('Submitting report:', formData);
    alert('Report submitted successfully! Thank you for helping your community.');
    navigate('/dashboard');
  };

  const handleLocationSelect = (lat: number, lng: number, address: string) => {
    setFormData({
      ...formData,
      location: { lat, lng, address }
    });
  };

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setFormData({
      ...formData,
      photos: [...formData.photos, ...files].slice(0, 5) // Max 5 photos
    });
  };

  const removePhoto = (index: number) => {
    setFormData({
      ...formData,
      photos: formData.photos.filter((_, i) => i !== index)
    });
  };

  const toggleAssistanceType = (type: string) => {
    setFormData({
      ...formData,
      assistanceNeeded: formData.assistanceNeeded.includes(type)
        ? formData.assistanceNeeded.filter(t => t !== type)
        : [...formData.assistanceNeeded, type]
    });
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1:
        return formData.disasterType && 
               (formData.disasterDetail || formData.customDisasterDetail) && 
               formData.description;
      case 2:
        return formData.location && formData.photos.length > 0;
      case 3:
        return formData.assistanceNeeded.length > 0 && formData.assistanceDescription;
      case 4:
        return true;
      default:
        return false;
    }
  };

  const stepTitles = [
    'Disaster Details',
    'Impact & Location',
    'Assistance Needs',
    'Review & Submit'
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Report a Disaster Impact
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Help your community by reporting disaster impacts and requesting assistance
          </p>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center justify-center mb-12">
          {[1, 2, 3, 4].map((step) => (
            <React.Fragment key={step}>
              <div className={`flex flex-col items-center ${step <= currentStep ? 'text-blue-600' : 'text-gray-400'}`}>
                <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-200 ${
                  step <= currentStep 
                    ? 'bg-blue-600 text-white border-blue-600 shadow-lg' 
                    : 'bg-white text-gray-400 border-gray-300'
                }`}>
                  {step}
                </div>
                <span className="text-xs mt-2 font-medium hidden sm:block">
                  {stepTitles[step - 1]}
                </span>
              </div>
              {step < 4 && (
                <div className={`w-16 h-1 mx-4 rounded-full transition-all duration-200 ${
                  step < currentStep ? 'bg-blue-600' : 'bg-gray-300'
                }`} />
              )}
            </React.Fragment>
          ))}
        </div>

        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
          {/* Step 1: Disaster Details */}
          {currentStep === 1 && (
            <div className="space-y-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">Disaster Details</h2>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-4">
                  Disaster Type
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.keys(disasterOptions).map((type) => (
                    <label key={type} className="flex items-center p-4 border border-gray-200 rounded-xl cursor-pointer hover:bg-blue-50 hover:border-blue-200 transition-all duration-200">
                      <input
                        type="radio"
                        name="disasterType"
                        value={type}
                        checked={formData.disasterType === type}
                        onChange={(e) => setFormData({ ...formData, disasterType: e.target.value as 'Natural' | 'Non-Natural', disasterDetail: '' })}
                        className="mr-3 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="font-medium">{type}</span>
                    </label>
                  ))}
                </div>
              </div>

              {formData.disasterType && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-4">
                    Specific Disaster
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {disasterOptions[formData.disasterType].map((detail) => (
                      <label key={detail} className="flex items-center p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-blue-50 hover:border-blue-200 transition-all duration-200">
                        <input
                          type="radio"
                          name="disasterDetail"
                          value={detail}
                          checked={formData.disasterDetail === detail}
                          onChange={(e) => setFormData({ ...formData, disasterDetail: e.target.value, customDisasterDetail: '' })}
                          className="mr-3 text-blue-600 focus:ring-blue-500"
                        />
                        <span>{detail}</span>
                      </label>
                    ))}
                  </div>
                  
                  {formData.disasterDetail === 'Other' && (
                    <input
                      type="text"
                      value={formData.customDisasterDetail}
                      onChange={(e) => setFormData({ ...formData, customDisasterDetail: e.target.value })}
                      placeholder="Please specify..."
                      className="mt-4 w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    />
                  )}
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  placeholder="Describe what happened and the current situation..."
                />
              </div>
            </div>
          )}

          {/* Step 2: Impact & Location */}
          {currentStep === 2 && (
            <div className="space-y-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">Impact & Location</h2>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-4">
                  <Target className="inline w-4 h-4 mr-1" />
                  Disaster Location
                </label>
                <div className="h-80 mb-4">
                  <LocationPicker
                    onLocationSelect={handleLocationSelect}
                    selectedLocation={formData.location}
                    height="100%"
                  />
                </div>
                {formData.location && (
                  <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-xl">
                    <div className="flex items-start space-x-3">
                      <MapPin size={16} className="text-green-600 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-green-800">Location Selected</p>
                        <p className="text-sm text-green-700">{formData.location.address}</p>
                        <p className="text-xs text-green-600">
                          Coordinates: {formData.location.lat.toFixed(6)}, {formData.location.lng.toFixed(6)}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-4">
                  Photos (Required - Max 5)
                </label>
                <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200">
                  <Upload size={48} className="mx-auto text-gray-400 mb-4" />
                  <label className="cursor-pointer">
                    <span className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium shadow-sm">
                      Choose Photos
                    </span>
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handlePhotoUpload}
                      className="hidden"
                    />
                  </label>
                  <p className="text-sm text-gray-600 mt-3">
                    Upload up to 5 photos (JPG, PNG, max 10MB each)
                  </p>
                </div>
                
                {formData.photos.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mt-6">
                    {formData.photos.map((photo, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={URL.createObjectURL(photo)}
                          alt={`Upload ${index + 1}`}
                          className="w-full h-24 object-cover rounded-xl"
                        />
                        <button
                          onClick={() => removePhoto(index)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-lg"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Step 3: Assistance Needs */}
          {currentStep === 3 && (
            <div className="space-y-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">Assistance Needed</h2>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-4">
                  What type of assistance do you need?
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {assistanceTypes.map((type) => (
                    <label key={type} className="flex items-center p-3 border border-gray-200 rounded-xl cursor-pointer hover:bg-blue-50 hover:border-blue-200 transition-all duration-200">
                      <input
                        type="checkbox"
                        checked={formData.assistanceNeeded.includes(type)}
                        onChange={() => toggleAssistanceType(type)}
                        className="mr-3 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm">{type}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Detailed Assistance Description
                </label>
                <textarea
                  value={formData.assistanceDescription}
                  onChange={(e) => setFormData({ ...formData, assistanceDescription: e.target.value })}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  placeholder="Please provide specific details about what help is needed, how many people are affected, any urgent requirements..."
                />
              </div>
            </div>
          )}

          {/* Step 4: Review */}
          {currentStep === 4 && (
            <div className="space-y-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">Review & Submit</h2>
              
              <div className="bg-gray-50 rounded-xl p-6 space-y-6">
                <div>
                  <h3 className="font-semibold text-gray-900">Disaster Information</h3>
                  <p className="text-gray-700">
                    {formData.disasterType} - {formData.disasterDetail || formData.customDisasterDetail}
                  </p>
                  <p className="text-gray-600 mt-1">{formData.description}</p>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-900">Location</h3>
                  <p className="text-gray-700">{formData.location?.address}</p>
                  <p className="text-sm text-gray-500">
                    {formData.location?.lat.toFixed(6)}, {formData.location?.lng.toFixed(6)}
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-900">Photos</h3>
                  <p className="text-gray-700">{formData.photos.length} photos uploaded</p>
                  {formData.photos.length > 0 && (
                    <div className="flex space-x-2 mt-2">
                      {formData.photos.slice(0, 3).map((photo, index) => (
                        <img
                          key={index}
                          src={URL.createObjectURL(photo)}
                          alt={`Preview ${index + 1}`}
                          className="w-16 h-16 object-cover rounded-lg"
                        />
                      ))}
                      {formData.photos.length > 3 && (
                        <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center text-sm text-gray-600">
                          +{formData.photos.length - 3}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                <div>
                  <h3 className="font-semibold text-gray-900">Assistance Needed</h3>
                  <p className="text-gray-700">{formData.assistanceNeeded.join(', ')}</p>
                  <p className="text-gray-600 mt-1">{formData.assistanceDescription}</p>
                </div>
              </div>

              <div className="bg-orange-50 border border-orange-200 rounded-xl p-4 flex items-start">
                <AlertTriangle size={20} className="text-orange-600 mr-3 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-orange-800">Important Notice</h3>
                  <p className="text-orange-700 text-sm mt-1">
                    Your report will be reviewed by our team before being published. We may contact you for additional information.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-8 border-t border-gray-100">
            <button
              onClick={handleBack}
              disabled={currentStep === 1}
              className="flex items-center px-6 py-3 border border-gray-300 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft size={20} className="mr-2" />
              Back
            </button>

            {currentStep < 4 ? (
              <button
                onClick={handleNext}
                disabled={!canProceed()}
                className="flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm"
              >
                Next
                <ChevronRight size={20} className="ml-2" />
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                className="flex items-center px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-sm"
              >
                Submit Report
              </button>
            )}
          </div>
        </div>
      </div>

      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
      />
    </div>
  );
};

export default CreateReport;