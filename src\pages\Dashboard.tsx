import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Plus, Eye, Edit, Clock, CheckCircle, XCircle, Heart } from 'lucide-react';
import { mockReports } from '../data/mockData';
import { format } from 'date-fns';

const Dashboard: React.FC = () => {
  const { user } = useAuth();

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">Please log in to access your dashboard.</p>
          <Link to="/" className="text-red-600 hover:text-red-700">
            Go to Home
          </Link>
        </div>
      </div>
    );
  }

  // Mock user reports (filter by user ID in real implementation)
  const userReports = mockReports.slice(0, 2);
  
  // Mock assistance provided
  const assistanceProvided = [
    {
      id: '1',
      reportTitle: 'Flooding in Downtown District',
      description: 'Provided emergency shelter coordination with Red Cross. Secured temporary housing for 8 families.',
      date: new Date('2024-01-16'),
      endorsed: true
    },
    {
      id: '2',
      reportTitle: 'Wildfire Damage Assessment',
      description: 'Coordinated evacuation transportation for 5 elderly residents.',
      date: new Date('2024-01-13'),
      endorsed: false
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'verified':
        return <CheckCircle size={16} className="text-green-600" />;
      case 'pending':
        return <Clock size={16} className="text-yellow-600" />;
      case 'rejected':
        return <XCircle size={16} className="text-red-600" />;
      default:
        return <Clock size={16} className="text-gray-600" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
              Welcome back, {user.name}
            </h1>
            <p className="text-xl text-gray-600">
              Manage your reports and assistance activities
            </p>
          </div>
          <Link
            to="/report/new"
            className="mt-4 md:mt-0 bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors flex items-center font-medium"
          >
            <Plus size={20} className="mr-2" />
            New Report
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center">
                <Eye size={24} />
              </div>
              <div className="ml-4">
                <h3 className="text-2xl font-bold text-gray-900">{userReports.length}</h3>
                <p className="text-gray-600">Reports Submitted</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-100 text-green-600 rounded-lg flex items-center justify-center">
                <CheckCircle size={24} />
              </div>
              <div className="ml-4">
                <h3 className="text-2xl font-bold text-gray-900">
                  {userReports.filter(r => r.status === 'verified').length}
                </h3>
                <p className="text-gray-600">Verified Reports</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-red-100 text-red-600 rounded-lg flex items-center justify-center">
                <Heart size={24} />
              </div>
              <div className="ml-4">
                <h3 className="text-2xl font-bold text-gray-900">{assistanceProvided.length}</h3>
                <p className="text-gray-600">Assistance Provided</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* My Reports */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">My Reports</h2>
              <Link
                to="/report/new"
                className="text-red-600 hover:text-red-700 font-medium text-sm"
              >
                Create New
              </Link>
            </div>

            <div className="space-y-4">
              {userReports.length > 0 ? (
                userReports.map((report) => (
                  <div key={report.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 mb-1">{report.title}</h3>
                        <p className="text-sm text-gray-600 line-clamp-2">{report.description}</p>
                      </div>
                      <img
                        src={report.photos[0]}
                        alt={report.title}
                        className="w-16 h-16 object-cover rounded-lg ml-4"
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(report.status)}
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(report.status)}`}>
                          {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                        </span>
                        <span className="text-xs text-gray-500">
                          {format(report.createdAt, 'MMM d, yyyy')}
                        </span>
                      </div>
                      
                      <div className="flex space-x-2">
                        <Link
                          to={`/reports/${report.id}`}
                          className="text-blue-600 hover:text-blue-700 text-sm flex items-center"
                        >
                          <Eye size={14} className="mr-1" />
                          View
                        </Link>
                        <button className="text-gray-600 hover:text-gray-700 text-sm flex items-center">
                          <Edit size={14} className="mr-1" />
                          Edit
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Eye size={48} className="mx-auto mb-4 text-gray-300" />
                  <p className="mb-2">No reports submitted yet</p>
                  <Link
                    to="/report/new"
                    className="text-red-600 hover:text-red-700 font-medium"
                  >
                    Submit your first report
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* My Assistance */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">My Assistance</h2>
              <Link
                to="/reports"
                className="text-red-600 hover:text-red-700 font-medium text-sm"
              >
                Help Others
              </Link>
            </div>

            <div className="space-y-4">
              {assistanceProvided.length > 0 ? (
                assistanceProvided.map((assistance) => (
                  <div key={assistance.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="font-semibold text-gray-900 mb-1">{assistance.reportTitle}</h3>
                      {assistance.endorsed && (
                        <div className="flex items-center text-green-600 text-sm">
                          <CheckCircle size={16} className="mr-1" />
                          Endorsed
                        </div>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-3">{assistance.description}</p>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">
                        {format(assistance.date, 'MMM d, yyyy')}
                      </span>
                      <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-700 text-sm">
                          View Report
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Heart size={48} className="mx-auto mb-4 text-gray-300" />
                  <p className="mb-2">No assistance provided yet</p>
                  <Link
                    to="/reports"
                    className="text-red-600 hover:text-red-700 font-medium"
                  >
                    Start helping others
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;