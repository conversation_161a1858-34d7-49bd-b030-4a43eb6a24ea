import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Edit3, FileText, CheckCircle, Clock, Users, BarChart3, TrendingUp, Eye, AlertTriangle, Plus, Send, Calendar, Globe, Image, Video, Megaphone, Shield, UserCheck, MessageSquare, Bell, Download, Upload, Search, Filter, Save, Trash2, Edit, Star, Flag, Archive, RefreshCw, X, MapPin, ExternalLink, UserX, Ban, CheckSquare, XCircle, Info, FileWarning as Warning, Zap, Database, Activity, User } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { format } from 'date-fns';

const EditorDashboard: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'verification' | 'content' | 'users'>('verification');
  const [selectedReport, setSelectedReport] = useState<any>(null);
  const [verificationNotes, setVerificationNotes] = useState('');
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [showUserActionModal, setShowUserActionModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // Mock data for pending reports
  const [pendingReports, setPendingReports] = useState([
    {
      id: 'report-001',
      title: 'Flooding in Downtown District',
      description: 'Severe flooding has affected multiple residential areas after heavy rainfall. Water levels reached 3-4 feet in some streets.',
      location: { address: 'Downtown District, Springfield, IL', lat: 39.7817, lng: -89.6501 },
      photos: [
        'https://images.pexels.com/photos/1118873/pexels-photo-1118873.jpeg?auto=compress&cs=tinysrgb&w=800',
        'https://images.pexels.com/photos/1592119/pexels-photo-1592119.jpeg?auto=compress&cs=tinysrgb&w=800'
      ],
      reporterName: 'Sarah Johnson',
      reporterId: 'user-123',
      reporterHistory: { totalReports: 5, verifiedReports: 4, flaggedReports: 0 },
      submittedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      disasterType: 'Flood',
      severity: 'High',
      status: 'pending',
      verificationSources: [
        { name: 'Weather.gov', status: 'confirmed', url: 'https://weather.gov/flood-warning' },
        { name: 'Local News', status: 'confirmed', url: 'https://localnews.com/flooding' },
        { name: 'Emergency Services', status: 'pending', url: null }
      ]
    },
    {
      id: 'report-002',
      title: 'Suspicious Fire Report',
      description: 'Large fire reported in industrial area. Claiming chemical explosion but no official reports.',
      location: { address: 'Industrial Park, Riverside, CA', lat: 33.9806, lng: -117.3755 },
      photos: [
        'https://images.pexels.com/photos/266487/pexels-photo-266487.jpeg?auto=compress&cs=tinysrgb&w=800'
      ],
      reporterName: 'Anonymous User',
      reporterId: 'user-456',
      reporterHistory: { totalReports: 1, verifiedReports: 0, flaggedReports: 2 },
      submittedAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
      disasterType: 'Fire',
      severity: 'Critical',
      status: 'pending',
      verificationSources: [
        { name: 'Fire Department', status: 'no_record', url: null },
        { name: 'Local News', status: 'no_record', url: null },
        { name: 'Social Media', status: 'conflicting', url: 'https://twitter.com/search' }
      ]
    },
    {
      id: 'report-003',
      title: 'Tornado Damage Assessment',
      description: 'EF2 tornado caused significant damage to residential area. Multiple homes damaged.',
      location: { address: 'Oakwood Subdivision, Moore, OK', lat: 35.3493, lng: -97.4867 },
      photos: [
        'https://images.pexels.com/photos/1118869/pexels-photo-1118869.jpeg?auto=compress&cs=tinysrgb&w=800'
      ],
      reporterName: 'Mike Davis',
      reporterId: 'user-789',
      reporterHistory: { totalReports: 12, verifiedReports: 11, flaggedReports: 0 },
      submittedAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
      disasterType: 'Tornado',
      severity: 'High',
      status: 'pending',
      verificationSources: [
        { name: 'National Weather Service', status: 'confirmed', url: 'https://weather.gov/tornado' },
        { name: 'Storm Prediction Center', status: 'confirmed', url: 'https://spc.noaa.gov' },
        { name: 'Local Emergency Management', status: 'confirmed', url: null }
      ]
    }
  ]);

  // Mock verified reports for content management
  const [verifiedReports, setVerifiedReports] = useState([
    {
      id: 'verified-001',
      title: 'Hurricane Milton Aftermath',
      status: 'verified',
      flagged: false,
      assistanceInteractions: 23,
      lastActivity: new Date(Date.now() - 1 * 60 * 60 * 1000),
      reporterName: 'Emergency Coordinator',
      inappropriateFlags: 0
    },
    {
      id: 'verified-002',
      title: 'Wildfire Evacuation Zone',
      status: 'verified',
      flagged: true,
      assistanceInteractions: 45,
      lastActivity: new Date(Date.now() - 3 * 60 * 60 * 1000),
      reporterName: 'Fire Department',
      inappropriateFlags: 2
    }
  ]);

  // Mock users for user management
  const [managedUsers, setManagedUsers] = useState([
    {
      id: 'user-123',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      status: 'active',
      totalReports: 5,
      verifiedReports: 4,
      flaggedReports: 0,
      violations: [],
      joinDate: new Date('2023-06-15'),
      lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000),
      riskLevel: 'low'
    },
    {
      id: 'user-456',
      name: 'Anonymous User',
      email: '<EMAIL>',
      status: 'active',
      totalReports: 1,
      verifiedReports: 0,
      flaggedReports: 2,
      violations: ['Fake Report', 'Misleading Information'],
      joinDate: new Date('2024-01-10'),
      lastActivity: new Date(Date.now() - 4 * 60 * 60 * 1000),
      riskLevel: 'high'
    },
    {
      id: 'user-789',
      name: 'Mike Davis',
      email: '<EMAIL>',
      status: 'active',
      totalReports: 12,
      verifiedReports: 11,
      flaggedReports: 0,
      violations: [],
      joinDate: new Date('2023-03-20'),
      lastActivity: new Date(Date.now() - 6 * 60 * 60 * 1000),
      riskLevel: 'low'
    }
  ]);

  const editorStats = [
    {
      name: 'Pending Verification',
      value: pendingReports.length.toString(),
      change: '+3 today',
      trend: 'up',
      icon: Clock,
      color: 'bg-orange-50 text-orange-600 border-orange-200',
      action: () => setActiveTab('verification')
    },
    {
      name: 'Verified Reports',
      value: verifiedReports.length.toString(),
      change: '+12 this week',
      trend: 'up',
      icon: CheckCircle,
      color: 'bg-green-50 text-green-600 border-green-200',
      action: () => setActiveTab('content')
    },
    {
      name: 'Flagged Content',
      value: verifiedReports.filter(r => r.flagged).length.toString(),
      change: '2 need review',
      trend: 'warning',
      icon: Flag,
      color: 'bg-red-50 text-red-600 border-red-200',
      action: () => setActiveTab('content')
    },
    {
      name: 'User Violations',
      value: managedUsers.filter(u => u.violations.length > 0).length.toString(),
      change: '1 high risk',
      trend: 'warning',
      icon: UserX,
      color: 'bg-purple-50 text-purple-600 border-purple-200',
      action: () => setActiveTab('users')
    }
  ];

  const handleVerifyReport = (reportId: string, decision: 'verified' | 'fake', notes: string) => {
    const report = pendingReports.find(r => r.id === reportId);
    if (!report) return;

    if (decision === 'verified') {
      // Move to verified reports
      setVerifiedReports(prev => [...prev, {
        id: reportId,
        title: report.title,
        status: 'verified',
        flagged: false,
        assistanceInteractions: 0,
        lastActivity: new Date(),
        reporterName: report.reporterName,
        inappropriateFlags: 0
      }]);
      
      // Remove from pending
      setPendingReports(prev => prev.filter(r => r.id !== reportId));
      
      alert(`Report "${report.title}" has been verified and published.`);
    } else {
      // Handle fake report
      setPendingReports(prev => prev.filter(r => r.id !== reportId));
      
      if (notes.includes('blacklist')) {
        alert(`Report marked as fake. User "${report.reporterName}" flagged for admin review and potential blacklisting.`);
      } else {
        alert(`Report "${report.title}" has been marked as fake and removed.`);
      }
    }

    setShowVerificationModal(false);
    setSelectedReport(null);
    setVerificationNotes('');
  };

  const handleUserAction = (userId: string, action: 'suspend' | 'flag_blacklist', reason: string) => {
    const user = managedUsers.find(u => u.id === userId);
    if (!user) return;

    if (action === 'suspend') {
      setManagedUsers(prev => prev.map(u => 
        u.id === userId 
          ? { ...u, status: 'suspended', violations: [...u.violations, reason] }
          : u
      ));
      alert(`User "${user.name}" has been temporarily suspended for: ${reason}`);
    } else {
      setManagedUsers(prev => prev.map(u => 
        u.id === userId 
          ? { ...u, riskLevel: 'critical', violations: [...u.violations, 'Flagged for Blacklist'] }
          : u
      ));
      alert(`User "${user.name}" has been flagged for admin review and potential blacklisting.`);
    }

    setShowUserActionModal(false);
    setSelectedUser(null);
  };

  const handleContentFlag = (reportId: string, action: 'flag' | 'hide') => {
    setVerifiedReports(prev => prev.map(r => 
      r.id === reportId 
        ? { ...r, flagged: action === 'flag' ? !r.flagged : r.flagged, inappropriateFlags: r.inappropriateFlags + 1 }
        : r
    ));
    
    const actionText = action === 'flag' ? 'flagged for review' : 'hidden from public view';
    alert(`Content has been ${actionText}.`);
  };

  const getVerificationSourceIcon = (status: string) => {
    switch (status) {
      case 'confirmed': return <CheckCircle size={16} className="text-green-600" />;
      case 'pending': return <Clock size={16} className="text-yellow-600" />;
      case 'no_record': return <XCircle size={16} className="text-red-600" />;
      case 'conflicting': return <AlertTriangle size={16} className="text-orange-600" />;
      default: return <Info size={16} className="text-gray-600" />;
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const filteredReports = pendingReports.filter(report => {
    const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.reporterName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || 
      (filterStatus === 'high_priority' && report.severity === 'Critical') ||
      (filterStatus === 'suspicious' && report.reporterHistory.flaggedReports > 0);
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Editor Workflow Dashboard</h1>
          <p className="text-gray-600">Report verification, content management, and user oversight</p>
        </div>
        <div className="mt-4 md:mt-0 flex items-center space-x-3">
          <div className="bg-white rounded-xl px-4 py-2 border border-gray-200 shadow-sm">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-gray-700">Live Monitoring</span>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {editorStats.map((stat, index) => (
          <div key={index} className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-shadow cursor-pointer" onClick={stat.action}>
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-xl ${stat.color}`}>
                <stat.icon size={24} />
              </div>
              <div className={`flex items-center text-sm font-medium ${
                stat.trend === 'up' ? 'text-green-600' : 
                stat.trend === 'warning' ? 'text-orange-600' : 'text-red-600'
              }`}>
                <TrendingUp size={16} className="mr-1" />
                {stat.change}
              </div>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</h3>
            <p className="text-gray-600 text-sm">{stat.name}</p>
          </div>
        ))}
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-2">
        <div className="flex space-x-1">
          {[
            { id: 'verification', label: 'Report Verification', icon: CheckSquare },
            { id: 'content', label: 'Content Management', icon: FileText },
            { id: 'users', label: 'User Management', icon: Users }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all ${
                activeTab === tab.id
                  ? 'bg-blue-100 text-blue-700 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <tab.icon size={16} />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Report Verification Tab */}
      {activeTab === 'verification' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search reports or reporters..."
                    className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              <div className="md:w-48">
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="w-full px-3 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Reports</option>
                  <option value="high_priority">High Priority</option>
                  <option value="suspicious">Suspicious</option>
                </select>
              </div>
            </div>
          </div>

          {/* Pending Reports */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredReports.map((report) => (
              <div key={report.id} className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm hover:shadow-lg transition-shadow">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      report.severity === 'Critical' ? 'bg-red-100 text-red-800' :
                      report.severity === 'High' ? 'bg-orange-100 text-orange-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {report.severity}
                    </span>
                    <span className="text-xs text-gray-500">
                      {format(report.submittedAt, 'MMM d, HH:mm')}
                    </span>
                  </div>
                  <div className={`w-3 h-3 rounded-full ${
                    report.reporterHistory.flaggedReports > 0 ? 'bg-red-400' : 'bg-green-400'
                  }`} title={report.reporterHistory.flaggedReports > 0 ? 'Suspicious Reporter' : 'Trusted Reporter'} />
                </div>

                <h3 className="font-semibold text-gray-900 mb-2">{report.title}</h3>
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">{report.description}</p>

                <div className="space-y-3 mb-4">
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPin size={14} className="mr-2 text-blue-500" />
                    {report.location.address}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <User size={14} className="mr-2 text-purple-500" />
                    {report.reporterName} ({report.reporterHistory.verifiedReports}/{report.reporterHistory.totalReports} verified)
                  </div>
                </div>

                {/* Verification Sources */}
                <div className="mb-4">
                  <h4 className="text-xs font-medium text-gray-700 mb-2">Verification Sources:</h4>
                  <div className="space-y-1">
                    {report.verificationSources.map((source, index) => (
                      <div key={index} className="flex items-center justify-between text-xs">
                        <div className="flex items-center space-x-2">
                          {getVerificationSourceIcon(source.status)}
                          <span className="text-gray-700">{source.name}</span>
                        </div>
                        {source.url && (
                          <a href={source.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-700">
                            <ExternalLink size={12} />
                          </a>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      setSelectedReport(report);
                      setShowVerificationModal(true);
                    }}
                    className="flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                  >
                    Review
                  </button>
                  <button className="px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                    <Eye size={16} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Content Management Tab */}
      {activeTab === 'content' && (
        <div className="space-y-6">
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Verified Reports Management</h2>
            <div className="space-y-4">
              {verifiedReports.map((report) => (
                <div key={report.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-4">
                    <div className={`w-3 h-3 rounded-full ${report.flagged ? 'bg-red-400' : 'bg-green-400'}`} />
                    <div>
                      <h3 className="font-medium text-gray-900">{report.title}</h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span>By {report.reporterName}</span>
                        <span>{report.assistanceInteractions} interactions</span>
                        <span>Last activity: {format(report.lastActivity, 'MMM d, HH:mm')}</span>
                        {report.inappropriateFlags > 0 && (
                          <span className="text-red-600">{report.inappropriateFlags} flags</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleContentFlag(report.id, 'flag')}
                      className={`p-2 rounded-lg transition-colors ${
                        report.flagged 
                          ? 'bg-red-100 text-red-600 hover:bg-red-200' 
                          : 'border border-gray-300 text-gray-600 hover:bg-gray-50'
                      }`}
                      title={report.flagged ? 'Unflag content' : 'Flag as inappropriate'}
                    >
                      <Flag size={16} />
                    </button>
                    <button
                      onClick={() => handleContentFlag(report.id, 'hide')}
                      className="p-2 border border-gray-300 text-gray-600 rounded-lg hover:bg-gray-50 transition-colors"
                      title="Hide content"
                    >
                      <Eye size={16} />
                    </button>
                    <button className="p-2 border border-gray-300 text-gray-600 rounded-lg hover:bg-gray-50 transition-colors">
                      <MessageSquare size={16} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* User Management Tab */}
      {activeTab === 'users' && (
        <div className="space-y-6">
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">User Oversight</h2>
            <div className="space-y-4">
              {managedUsers.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-4">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-medium ${
                      user.riskLevel === 'low' ? 'bg-green-500' :
                      user.riskLevel === 'medium' ? 'bg-yellow-500' :
                      user.riskLevel === 'high' ? 'bg-orange-500' :
                      'bg-red-500'
                    }`}>
                      {user.name.charAt(0)}
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <h3 className="font-medium text-gray-900">{user.name}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getRiskLevelColor(user.riskLevel)}`}>
                          {user.riskLevel} risk
                        </span>
                        {user.status === 'suspended' && (
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Suspended
                          </span>
                        )}
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span>{user.verifiedReports}/{user.totalReports} verified reports</span>
                        <span>{user.flaggedReports} flagged</span>
                        <span>Joined {format(user.joinDate, 'MMM yyyy')}</span>
                        {user.violations.length > 0 && (
                          <span className="text-red-600">{user.violations.length} violations</span>
                        )}
                      </div>
                      {user.violations.length > 0 && (
                        <div className="mt-1">
                          <div className="flex flex-wrap gap-1">
                            {user.violations.map((violation, index) => (
                              <span key={index} className="px-2 py-0.5 bg-red-100 text-red-700 text-xs rounded">
                                {violation}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => {
                        setSelectedUser(user);
                        setShowUserActionModal(true);
                      }}
                      className="px-3 py-1 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm"
                      disabled={user.status === 'suspended'}
                    >
                      {user.status === 'suspended' ? 'Suspended' : 'Suspend'}
                    </button>
                    <button
                      onClick={() => {
                        setSelectedUser(user);
                        setShowUserActionModal(true);
                      }}
                      className="px-3 py-1 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
                    >
                      Flag for Blacklist
                    </button>
                    <button className="p-2 border border-gray-300 text-gray-600 rounded-lg hover:bg-gray-50 transition-colors">
                      <Eye size={16} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Verification Modal */}
      {showVerificationModal && selectedReport && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-8">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-semibold text-gray-900">Report Verification</h3>
                <button
                  onClick={() => setShowVerificationModal(false)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <X size={20} />
                </button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Report Details */}
                <div>
                  <h4 className="font-semibold text-gray-900 mb-4">Report Details</h4>
                  <div className="space-y-4">
                    <div>
                      <h5 className="font-medium text-gray-700">Title</h5>
                      <p className="text-gray-900">{selectedReport.title}</p>
                    </div>
                    <div>
                      <h5 className="font-medium text-gray-700">Description</h5>
                      <p className="text-gray-900">{selectedReport.description}</p>
                    </div>
                    <div>
                      <h5 className="font-medium text-gray-700">Location</h5>
                      <p className="text-gray-900">{selectedReport.location.address}</p>
                    </div>
                    <div>
                      <h5 className="font-medium text-gray-700">Reporter</h5>
                      <p className="text-gray-900">{selectedReport.reporterName}</p>
                      <p className="text-sm text-gray-600">
                        History: {selectedReport.reporterHistory.verifiedReports}/{selectedReport.reporterHistory.totalReports} verified, 
                        {selectedReport.reporterHistory.flaggedReports} flagged
                      </p>
                    </div>
                  </div>

                  {/* Photos */}
                  <div className="mt-6">
                    <h5 className="font-medium text-gray-700 mb-2">Photos</h5>
                    <div className="grid grid-cols-2 gap-2">
                      {selectedReport.photos.map((photo: string, index: number) => (
                        <img
                          key={index}
                          src={photo}
                          alt={`Report photo ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg"
                        />
                      ))}
                    </div>
                  </div>
                </div>

                {/* Verification Sources */}
                <div>
                  <h4 className="font-semibold text-gray-900 mb-4">Verification Sources</h4>
                  <div className="space-y-3 mb-6">
                    {selectedReport.verificationSources.map((source: any, index: number) => (
                      <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div className="flex items-center space-x-3">
                          {getVerificationSourceIcon(source.status)}
                          <div>
                            <p className="font-medium text-gray-900">{source.name}</p>
                            <p className="text-sm text-gray-600 capitalize">{source.status.replace('_', ' ')}</p>
                          </div>
                        </div>
                        {source.url && (
                          <a
                            href={source.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-700"
                          >
                            <ExternalLink size={16} />
                          </a>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Verification Notes */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Verification Notes
                    </label>
                    <textarea
                      value={verificationNotes}
                      onChange={(e) => setVerificationNotes(e.target.value)}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Add notes about verification process, sources checked, concerns..."
                    />
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-3">
                    <button
                      onClick={() => handleVerifyReport(selectedReport.id, 'verified', verificationNotes)}
                      className="w-full bg-green-600 text-white py-3 px-4 rounded-xl hover:bg-green-700 transition-colors font-medium flex items-center justify-center"
                    >
                      <CheckCircle size={16} className="mr-2" />
                      Verify & Publish Report
                    </button>
                    <button
                      onClick={() => handleVerifyReport(selectedReport.id, 'fake', verificationNotes)}
                      className="w-full bg-red-600 text-white py-3 px-4 rounded-xl hover:bg-red-700 transition-colors font-medium flex items-center justify-center"
                    >
                      <XCircle size={16} className="mr-2" />
                      Mark as Fake
                    </button>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="blacklistUser"
                        onChange={(e) => {
                          if (e.target.checked) {
                            setVerificationNotes(prev => prev + ' [RECOMMEND BLACKLIST USER]');
                          } else {
                            setVerificationNotes(prev => prev.replace(' [RECOMMEND BLACKLIST USER]', ''));
                          }
                        }}
                        className="mr-2 rounded border-gray-300 text-red-600 focus:ring-red-500"
                      />
                      <label htmlFor="blacklistUser" className="text-sm text-gray-700">
                        Recommend user for blacklisting (requires admin approval)
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* User Action Modal */}
      {showUserActionModal && selectedUser && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full">
            <div className="p-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">User Action</h3>
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-2">{selectedUser.name}</h4>
                <p className="text-sm text-gray-600">{selectedUser.email}</p>
                <div className="mt-2 flex flex-wrap gap-2">
                  {selectedUser.violations.map((violation: string, index: number) => (
                    <span key={index} className="px-2 py-1 bg-red-100 text-red-700 text-xs rounded">
                      {violation}
                    </span>
                  ))}
                </div>
              </div>

              <div className="space-y-3">
                <button
                  onClick={() => handleUserAction(selectedUser.id, 'suspend', 'Policy violation')}
                  className="w-full bg-orange-600 text-white py-3 px-4 rounded-xl hover:bg-orange-700 transition-colors font-medium"
                  disabled={selectedUser.status === 'suspended'}
                >
                  Temporarily Suspend User
                </button>
                <button
                  onClick={() => handleUserAction(selectedUser.id, 'flag_blacklist', 'Multiple violations - recommend blacklist')}
                  className="w-full bg-red-600 text-white py-3 px-4 rounded-xl hover:bg-red-700 transition-colors font-medium"
                >
                  Flag for Blacklisting
                </button>
                <button
                  onClick={() => setShowUserActionModal(false)}
                  className="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-xl hover:bg-gray-50 transition-colors font-medium"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EditorDashboard;