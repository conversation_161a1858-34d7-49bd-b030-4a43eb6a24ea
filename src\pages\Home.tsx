import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { AlertTriangle, Users, CheckCircle, MapPin, Calendar, Eye, ArrowRight, Shield, Heart, Clock, Star, ChevronRight, Award, Globe, Zap, X, Play, CheckSquare, Camera, MessageCircle, TrendingUp, BarChart3, Activity, ChevronUp, Navigation, ZoomIn, ZoomOut, Crosshair } from 'lucide-react';
import { mockReports } from '../data/mockData';
import { format } from 'date-fns';
import ChatWidget from '../components/Chat/ChatWidget';
import ViewReportsButton, { ViewReportsCard } from '../components/Common/ViewReportsButton';
import ReportMap from '../components/Map/ReportMap';

const Home: React.FC = () => {
  const recentReports = mockReports.slice(0, 3);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const [lastScrollAction, setLastScrollAction] = useState(0);
  const mapRef = useRef<HTMLDivElement>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<{lat: number, lng: number} | null>(null);
  const [mapZoom, setMapZoom] = useState(4);

  // Debounced scroll handler
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      setShowScrollTop(scrollTop > 300);
      
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setIsScrolling(false);
      }, 150);
      
      setIsScrolling(true);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  // Smooth scroll to top with safety features
  const scrollToTop = () => {
    const now = Date.now();
    if (now - lastScrollAction < 500) return; // Prevent double-clicks
    
    setLastScrollAction(now);
    
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // Get current location
  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      alert('Geolocation is not supported by this browser.');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        setCurrentLocation({
          lat: position.coords.latitude,
          lng: position.coords.longitude
        });
        setMapZoom(10);
      },
      (error) => {
        console.error('Error getting location:', error);
        alert('Unable to get your location. Please enable location services.');
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000
      }
    );
  };

  // Map loading simulation
  useEffect(() => {
    const timer = setTimeout(() => {
      setMapLoaded(true);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  const testimonials = [
    {
      id: 1,
      name: "Sarah Chen",
      role: "Emergency Coordinator",
      location: "San Francisco, CA",
      avatar: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1",
      content: "DisasterWatch helped us coordinate relief efforts during the recent flooding. The real-time reporting system was invaluable for our response team.",
      rating: 5
    },
    {
      id: 2,
      name: "Michael Rodriguez",
      role: "Community Volunteer",
      location: "Austin, TX",
      avatar: "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1",
      content: "I've been able to help 12 families through this platform. It's amazing how technology can bring communities together during tough times.",
      rating: 5
    },
    {
      id: 3,
      name: "Dr. Emily Watson",
      role: "Disaster Response Specialist",
      location: "Miami, FL",
      avatar: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&dpr=1",
      content: "The verification system ensures accurate information reaches the right people. This platform has revolutionized how we handle disaster response.",
      rating: 5
    }
  ];

  const partners = [
    { name: "American Red Cross", logo: "https://images.pexels.com/photos/6646918/pexels-photo-6646918.jpeg?auto=compress&cs=tinysrgb&w=120&h=60&dpr=1" },
    { name: "FEMA", logo: "https://images.pexels.com/photos/8815176/pexels-photo-8815176.jpeg?auto=compress&cs=tinysrgb&w=120&h=60&dpr=1" },
    { name: "Salvation Army", logo: "https://images.pexels.com/photos/3862627/pexels-photo-3862627.jpeg?auto=compress&cs=tinysrgb&w=120&h=60&dpr=1" },
    { name: "United Way", logo: "https://images.pexels.com/photos/6646918/pexels-photo-6646918.jpeg?auto=compress&cs=tinysrgb&w=120&h=60&dpr=1" },
    { name: "Doctors Without Borders", logo: "https://images.pexels.com/photos/8815176/pexels-photo-8815176.jpeg?auto=compress&cs=tinysrgb&w=120&h=60&dpr=1" },
    { name: "World Health Organization", logo: "https://images.pexels.com/photos/3862627/pexels-photo-3862627.jpeg?auto=compress&cs=tinysrgb&w=120&h=60&dpr=1" }
  ];

  const stats = [
    { label: "Reports Submitted", value: "2,847", icon: AlertTriangle, color: "text-red-600" },
    { label: "Lives Helped", value: "12,450", icon: Users, color: "text-blue-600" },
    { label: "Verified Reports", value: "2,189", icon: CheckCircle, color: "text-green-600" },
    { label: "Response Time", value: "< 2hrs", icon: Clock, color: "text-purple-600" }
  ];

  // Organizations/Features data
  const organizationFeatures = [
    {
      id: 1,
      number: "01",
      title: "Real-Time Disaster Reporting",
      description: "Report disasters instantly with photos, location data, and detailed descriptions to alert your community.",
      icon: AlertTriangle,
      color: "from-red-500 to-orange-500",
      bgColor: "bg-red-50",
      stats: {
        response: "< 2min",
        reports: "2,847",
        active: "24/7"
      },
      features: ["Instant photo upload", "GPS location tracking", "Real-time alerts", "Community notifications"]
    },
    {
      id: 2,
      number: "02",
      title: "Verified Information System",
      description: "All reports are verified by our expert team and local partners to ensure accuracy and reliability.",
      icon: Shield,
      color: "from-blue-500 to-indigo-500",
      bgColor: "bg-blue-50",
      stats: {
        verified: "99.2%",
        experts: "150+",
        partners: "50+"
      },
      features: ["Expert verification", "Partner validation", "Accuracy guarantee", "Trust indicators"]
    },
    {
      id: 3,
      number: "03",
      title: "Community Support Network",
      description: "Connect with neighbors and volunteers ready to provide assistance during emergencies.",
      icon: Users,
      color: "from-green-500 to-emerald-500",
      bgColor: "bg-green-50",
      stats: {
        volunteers: "12,450",
        communities: "150+",
        assistance: "5,000+"
      },
      features: ["Volunteer matching", "Skill-based help", "Local coordination", "Resource sharing"]
    },
    {
      id: 4,
      number: "04",
      title: "Location-Based Alerts",
      description: "Receive notifications about disasters and assistance needs in your specific area.",
      icon: MapPin,
      color: "from-purple-500 to-violet-500",
      bgColor: "bg-purple-50",
      stats: {
        coverage: "50 states",
        alerts: "Real-time",
        precision: "GPS-based"
      },
      features: ["Geo-targeted alerts", "Custom radius", "Multi-channel delivery", "Emergency broadcasts"]
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-10 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"></div>
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center px-6 py-3 bg-white/10 backdrop-blur-sm rounded-full text-blue-100 text-sm font-medium border border-white/20 mb-8">
              <Zap size={16} className="mr-2" />
              Trusted by 50,000+ community members worldwide
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold leading-tight mb-8">
              Unite Communities in
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-300 via-purple-300 to-indigo-300">
                Times of Crisis
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-blue-100 leading-relaxed mb-12 max-w-3xl mx-auto">
              Report disasters, offer assistance, and build resilience together. Every voice matters in community recovery.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <Link
                to="/report/new"
                className="group bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-10 py-5 rounded-2xl text-lg font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all transform hover:-translate-y-1 hover:shadow-2xl flex items-center justify-center border border-blue-400/20"
              >
                Report an Impact
                <ArrowRight size={20} className="ml-2 group-hover:translate-x-1 transition-transform" />
              </Link>
              
              <ViewReportsButton 
                variant="outline" 
                size="lg" 
                className="border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm"
              />
            </div>

            {/* Stats - Flexbox Layout */}
            <div className="flex flex-wrap justify-center gap-6 md:gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="flex-shrink-0 text-center min-w-[120px]">
                  <div className="text-3xl md:text-4xl font-bold text-white mb-2">{stat.value}</div>
                  <div className="text-blue-200 text-sm">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Trust Indicators */}
      <section className="py-16 bg-gray-50 border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <p className="text-gray-600 font-medium text-lg mb-4">Trusted by leading organizations worldwide</p>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"></div>
          </div>
          
          {/* Partners - Flexbox Layout */}
          <div className="flex flex-wrap justify-center items-center gap-6 md:gap-8">
            {partners.map((partner, index) => (
              <div key={index} className="flex flex-col items-center space-y-3 group flex-shrink-0 min-w-[120px]">
                <img
                  src={partner.logo}
                  alt={partner.name}
                  className="h-12 w-20 object-cover rounded-lg grayscale group-hover:grayscale-0 transition-all duration-300 shadow-sm"
                  loading="lazy"
                  width="80"
                  height="48"
                />
                <span className="text-gray-700 font-medium text-sm text-center">{partner.name}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Organizations Features Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Everything You Need for
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                Disaster Response
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Comprehensive tools and features designed to connect communities and streamline disaster response efforts
            </p>
          </div>

          {/* Features Grid */}
          <div className="space-y-16">
            {organizationFeatures.map((feature, index) => (
              <div key={feature.id} className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center gap-12 lg:gap-16`}>
                {/* Content Side */}
                <div className="flex-1 space-y-6">
                  <div className="flex items-center space-x-4">
                    <div className="text-6xl font-bold text-gray-200">{feature.number}</div>
                    <div className={`p-4 rounded-2xl bg-gradient-to-br ${feature.color} text-white shadow-lg`}>
                      <feature.icon size={32} />
                    </div>
                  </div>
                  
                  <h3 className="text-3xl md:text-4xl font-bold text-gray-900 leading-tight">
                    {feature.title}
                  </h3>
                  
                  <p className="text-xl text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>

                  {/* Stats */}
                  <div className="flex flex-wrap gap-6">
                    {Object.entries(feature.stats).map(([key, value]) => (
                      <div key={key} className="text-center">
                        <div className="text-2xl font-bold text-gray-900">{value}</div>
                        <div className="text-sm text-gray-600 capitalize">{key}</div>
                      </div>
                    ))}
                  </div>

                  {/* Features List */}
                  <div className="grid grid-cols-2 gap-3">
                    {feature.features.map((item, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <CheckCircle size={16} className="text-green-500" />
                        <span className="text-gray-700 text-sm">{item}</span>
                      </div>
                    ))}
                  </div>

                  <button className={`inline-flex items-center px-8 py-4 bg-gradient-to-r ${feature.color} text-white rounded-2xl hover:shadow-lg transition-all duration-200 font-semibold`}>
                    Learn More
                    <ArrowRight size={20} className="ml-2" />
                  </button>
                </div>

                {/* Visual Side */}
                <div className="flex-1">
                  <div className={`${feature.bgColor} rounded-3xl p-8 border border-gray-100 shadow-sm`}>
                    <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                      <div className="flex items-center space-x-3 mb-4">
                        <div className={`p-2 rounded-lg bg-gradient-to-br ${feature.color} text-white`}>
                          <feature.icon size={20} />
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900">{feature.title}</h4>
                          <div className="flex items-center space-x-2">
                            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                            <span className="text-sm text-gray-600">Active Now</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="space-y-3">
                        <div className="text-sm text-gray-600">
                          {feature.description.substring(0, 80)}...
                        </div>
                        
                        <div className="flex justify-between text-sm">
                          {Object.entries(feature.stats).slice(0, 3).map(([key, value]) => (
                            <div key={key} className="text-center">
                              <div className="font-bold text-gray-900">{value}</div>
                              <div className="text-gray-500 capitalize text-xs">{key}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Interactive Map Section */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Real-Time Disaster
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-red-500 to-orange-500">
                Monitoring Map
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Track active disasters and community response efforts across the nation in real-time
            </p>
          </div>

          <div className="bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden">
            <div className="p-8">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
                <div>
                  <h3 className="text-2xl font-semibold text-gray-900 mb-2">Live Disaster Reports</h3>
                  <p className="text-gray-600">Click on any marker to view detailed information</p>
                </div>
                <div className="mt-4 lg:mt-0 flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                    <span className="text-sm text-gray-600">Active Reports</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">Verified</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <span className="text-sm text-gray-600">Pending</span>
                  </div>
                </div>
              </div>
              
              {/* Enhanced Map Container with Controls */}
              <div className="relative rounded-2xl overflow-hidden border border-gray-200 shadow-lg">
                {/* Map Loading Placeholder */}
                {!mapLoaded && (
                  <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
                    <div className="text-center">
                      <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                      <p className="text-gray-600 font-medium">Loading interactive map...</p>
                      <div className="w-32 h-2 bg-gray-200 rounded-full mx-auto mt-2 overflow-hidden">
                        <div className="h-full bg-blue-500 rounded-full animate-pulse" style={{ width: '70%' }}></div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Map Controls */}
                <div className="absolute top-4 right-4 z-20 flex flex-col space-y-2">
                  {/* Current Location Button */}
                  <button
                    onClick={getCurrentLocation}
                    className="group bg-white/90 backdrop-blur-sm p-3 rounded-xl shadow-lg border border-gray-200 hover:bg-white hover:shadow-xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    aria-label="Get current location"
                    style={{ minWidth: '44px', minHeight: '44px' }}
                  >
                    <Navigation size={18} className="text-blue-600 group-hover:text-blue-700 transition-colors" />
                  </button>

                  {/* Zoom Controls */}
                  <div className="bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                    <button
                      onClick={() => setMapZoom(prev => Math.min(prev + 1, 18))}
                      className="block w-full p-3 hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
                      aria-label="Zoom in"
                      style={{ minWidth: '44px', minHeight: '44px' }}
                    >
                      <ZoomIn size={18} className="text-gray-600 hover:text-gray-800 transition-colors mx-auto" />
                    </button>
                    <div className="border-t border-gray-200"></div>
                    <button
                      onClick={() => setMapZoom(prev => Math.max(prev - 1, 1))}
                      className="block w-full p-3 hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
                      aria-label="Zoom out"
                      style={{ minWidth: '44px', minHeight: '44px' }}
                    >
                      <ZoomOut size={18} className="text-gray-600 hover:text-gray-800 transition-colors mx-auto" />
                    </button>
                  </div>

                  {/* Map Type Toggle */}
                  <button
                    className="bg-white/90 backdrop-blur-sm p-3 rounded-xl shadow-lg border border-gray-200 hover:bg-white hover:shadow-xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    aria-label="Toggle map view"
                    style={{ minWidth: '44px', minHeight: '44px' }}
                  >
                    <Globe size={18} className="text-gray-600 hover:text-gray-800 transition-colors" />
                  </button>
                </div>

                {/* Map Legend */}
                <div className="absolute bottom-4 left-4 z-20 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200 p-4">
                  <h4 className="font-semibold text-gray-900 mb-2 text-sm">Map Legend</h4>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <span className="text-xs text-gray-600">Critical</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                      <span className="text-xs text-gray-600">High Priority</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <span className="text-xs text-gray-600">Medium</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-xs text-gray-600">Resolved</span>
                    </div>
                  </div>
                </div>

                {/* Current Location Indicator */}
                {currentLocation && (
                  <div className="absolute top-4 left-4 z-20 bg-blue-500 text-white px-3 py-2 rounded-lg shadow-lg text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <Crosshair size={14} />
                      <span>Your Location</span>
                    </div>
                  </div>
                )}

                {/* Main Map */}
                <div 
                  ref={mapRef}
                  className="transition-opacity duration-500"
                  style={{ opacity: mapLoaded ? 1 : 0 }}
                >
                  <ReportMap
                    reports={recentReports}
                    height="500px"
                  />
                </div>
              </div>
              
              <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                <ViewReportsButton 
                  size="lg"
                  className="flex-1 sm:flex-none transform hover:scale-105 transition-transform duration-200"
                />
                <Link
                  to="/report/new"
                  className="flex-1 sm:flex-none bg-gradient-to-r from-red-500 to-orange-500 text-white px-8 py-4 rounded-xl hover:from-red-600 hover:to-orange-600 transition-all duration-200 font-semibold flex items-center justify-center transform hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  <AlertTriangle size={20} className="mr-2" />
                  Report New Incident
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Recent Reports */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-16 gap-6">
            <div className="flex-1">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Recent Verified Reports
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl">
                Stay informed about recent disasters and community needs in your area
              </p>
            </div>
            <div className="flex-shrink-0">
              <ViewReportsButton 
                className="hidden md:flex"
                showIcon={false}
              />
            </div>
          </div>

          {/* Reports - Flexbox Layout */}
          <div className="flex flex-wrap gap-6 lg:gap-8 justify-center">
            {recentReports.map((report) => (
              <div key={report.id} className="flex-1 min-w-[300px] max-w-[400px] bg-white rounded-3xl shadow-sm hover:shadow-xl transition-all duration-500 overflow-hidden border border-gray-100 group">
                <div className="relative h-56 overflow-hidden">
                  <img
                    src={report.photos[0]}
                    alt={report.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                    loading="lazy"
                    width="400"
                    height="224"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                  <div className="absolute top-6 left-6">
                    <span className="bg-gradient-to-r from-red-500 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                      {report.disasterDetail}
                    </span>
                  </div>
                  <div className="absolute top-6 right-6">
                    <div className="bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-xs font-semibold text-gray-700 flex items-center space-x-1">
                      <CheckCircle size={12} className="text-green-500" />
                      <span>Verified</span>
                    </div>
                  </div>
                  <div className="absolute bottom-6 left-6 right-6">
                    <h3 className="text-xl font-bold text-white mb-2 group-hover:text-blue-200 transition-colors">
                      {report.title}
                    </h3>
                  </div>
                </div>
                
                <div className="p-8">
                  <p className="text-gray-600 mb-6 leading-relaxed line-clamp-3">
                    {report.description}
                  </p>
                  
                  <div className="flex flex-wrap items-center text-sm text-gray-500 mb-6 gap-4">
                    <div className="flex items-center space-x-2">
                      <MapPin size={16} className="text-blue-500" />
                      <span>{report.location.address.split(',')[0]}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar size={16} className="text-green-500" />
                      <span>{format(report.createdAt, 'MMM d')}</span>
                    </div>
                  </div>
                  
                  <Link
                    to={`/reports/${report.id}`}
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold group/link"
                  >
                    <Eye size={16} className="mr-2" />
                    View Details
                    <ArrowRight size={14} className="ml-2 group-hover/link:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-16 md:hidden">
            <ViewReportsButton size="lg" />
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Trusted by Communities
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                Nationwide
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See how DisasterWatch is making a difference in disaster response and community resilience
            </p>
          </div>

          {/* Testimonials - Flexbox Layout */}
          <div className="flex flex-wrap gap-6 lg:gap-8 justify-center">
            {testimonials.map((testimonial) => (
              <div key={testimonial.id} className="flex-1 min-w-[300px] max-w-[400px] bg-white rounded-3xl p-8 border border-gray-100 hover:shadow-xl transition-all duration-500 group">
                <div className="flex items-center mb-6">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} size={18} className="text-yellow-400 fill-current" />
                  ))}
                </div>
                <blockquote className="text-gray-700 mb-8 leading-relaxed text-lg">
                  "{testimonial.content}"
                </blockquote>
                <div className="flex items-center">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    className="w-14 h-14 rounded-full object-cover mr-4 ring-4 ring-blue-100"
                    loading="lazy"
                    width="56"
                    height="56"
                  />
                  <div>
                    <div className="font-semibold text-gray-900 text-lg">{testimonial.name}</div>
                    <div className="text-sm text-gray-600">{testimonial.role}</div>
                    <div className="text-xs text-gray-500">{testimonial.location}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-10 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"></div>
        </div>
        
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-8">
            Ready to Make a
            <br />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-300 to-purple-300">
              Difference?
            </span>
          </h2>
          <p className="text-xl text-blue-100 mb-12 leading-relaxed max-w-2xl mx-auto">
            Join thousands of community members who are building resilience and helping neighbors in times of need.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              to="/report/new"
              className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-10 py-5 rounded-2xl text-lg font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all transform hover:-translate-y-1 hover:shadow-2xl flex items-center justify-center border border-blue-400/20"
            >
              Report an Impact
              <AlertTriangle size={20} className="ml-3" />
            </Link>
            <ViewReportsButton 
              variant="outline" 
              size="lg" 
              className="border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm"
            />
          </div>
        </div>
      </section>

      {/* Enhanced Footer with Scroll to Top */}
      <footer 
        className="fixed bottom-0 left-0 right-0 z-40 bg-white/95 backdrop-blur-sm border-t border-gray-200 shadow-lg"
        style={{ padding: '24px' }}
      >
        <div className="max-w-7xl mx-auto flex items-center justify-between" style={{ gap: '16px' }}>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Shield size={20} className="text-blue-600" />
              <span className="font-semibold text-gray-900">DisasterWatch</span>
            </div>
            <div className="hidden sm:block text-sm text-gray-600">
              © 2024 Community Safety Platform
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="hidden md:flex items-center space-x-4 text-sm text-gray-600">
              <Link to="/privacy" className="hover:text-gray-900 transition-colors">Privacy</Link>
              <Link to="/terms" className="hover:text-gray-900 transition-colors">Terms</Link>
              <Link to="/contact" className="hover:text-gray-900 transition-colors">Contact</Link>
            </div>
            
            {/* Scroll to Top Button */}
            <button
              onClick={scrollToTop}
              className={`group bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-98 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                showScrollTop ? 'translate-y-0 opacity-100' : 'translate-y-2 opacity-0'
              }`}
              aria-label="Scroll to top"
              style={{ minWidth: '44px', minHeight: '44px' }}
              disabled={isScrolling}
            >
              <ChevronUp 
                size={20} 
                className={`transition-transform duration-300 ${isScrolling ? 'animate-pulse' : 'group-hover:-translate-y-0.5'}`} 
              />
            </button>
          </div>
        </div>
      </footer>

      {/* Chat Widget */}
      <ChatWidget position="bottom-right" />

      {/* Bottom Padding for Fixed Footer */}
      <div style={{ height: '120px' }}></div>
    </div>
  );
};

export default Home;