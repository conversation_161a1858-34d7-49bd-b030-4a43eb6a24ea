import React, { useState } from 'react';
import { Bell, AlertTriangle, Info, CheckCircle } from 'lucide-react';
import NotificationMessage from '../components/Common/NotificationMessage';

const NotificationDemo: React.FC = () => {
  const [selectedType, setSelectedType] = useState<'emergency' | 'update' | 'reminder' | 'announcement'>('announcement');

  const notificationTypes = [
    { 
      type: 'emergency' as const, 
      label: 'Emergency Alert', 
      icon: AlertTriangle, 
      color: 'bg-red-100 text-red-700 border-red-200',
      description: 'Urgent notifications requiring immediate action'
    },
    { 
      type: 'update' as const, 
      label: 'System Update', 
      icon: Info, 
      color: 'bg-blue-100 text-blue-700 border-blue-200',
      description: 'Important system or service updates'
    },
    { 
      type: 'reminder' as const, 
      label: 'Reminder', 
      icon: Bell, 
      color: 'bg-amber-100 text-amber-700 border-amber-200',
      description: 'Upcoming events and deadlines'
    },
    { 
      type: 'announcement' as const, 
      label: 'Announcement', 
      icon: CheckCircle, 
      color: 'bg-green-100 text-green-700 border-green-200',
      description: 'General announcements and news'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Professional Notification Messages
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive notification system with clear messaging, proper formatting, and professional presentation
          </p>
        </div>

        {/* Notification Type Selector */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">Select Notification Type</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {notificationTypes.map((type) => {
              const IconComponent = type.icon;
              const isSelected = selectedType === type.type;
              
              return (
                <button
                  key={type.type}
                  onClick={() => setSelectedType(type.type)}
                  className={`p-6 rounded-xl border-2 transition-all duration-200 text-left hover:shadow-md ${
                    isSelected 
                      ? `${type.color} border-current shadow-sm` 
                      : 'bg-gray-50 text-gray-600 border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center space-x-3 mb-3">
                    <div className={`p-2 rounded-lg ${isSelected ? 'bg-white/50' : 'bg-white'}`}>
                      <IconComponent size={20} />
                    </div>
                    <h3 className="font-semibold">{type.label}</h3>
                  </div>
                  <p className="text-sm opacity-75">{type.description}</p>
                </button>
              );
            })}
          </div>
        </div>

        {/* Notification Display */}
        <div className="mb-12">
          <NotificationMessage type={selectedType} />
        </div>

        {/* Features Overview */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">Notification Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">📋 Content Structure</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Clear, descriptive subject lines</li>
                <li>• Priority indicators and notification IDs</li>
                <li>• Structured main message content</li>
                <li>• Action-oriented information</li>
                <li>• Professional closing statements</li>
              </ul>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">📅 Time & Location</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Specific date and time details</li>
                <li>• Location information with icons</li>
                <li>• Deadline and time-sensitive alerts</li>
                <li>• Duration and scheduling details</li>
                <li>• Timezone specifications</li>
              </ul>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">📞 Contact & Actions</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Multiple contact methods</li>
                <li>• Emergency hotline numbers</li>
                <li>• Email support addresses</li>
                <li>• Office hours and availability</li>
                <li>• Clear call-to-action buttons</li>
              </ul>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">🎨 Visual Design</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Color-coded notification types</li>
                <li>• Consistent iconography</li>
                <li>• Proper spacing and typography</li>
                <li>• Responsive layout design</li>
                <li>• Accessibility considerations</li>
              </ul>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">⚡ Interactive Elements</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Dismissible notifications</li>
                <li>• Action buttons with hover effects</li>
                <li>• Type switching demonstration</li>
                <li>• Smooth transitions and animations</li>
                <li>• Mobile-responsive interactions</li>
              </ul>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">🔒 Professional Standards</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Formal business communication tone</li>
                <li>• Proper information hierarchy</li>
                <li>• Contact verification details</li>
                <li>• Organizational branding</li>
                <li>• Legal and compliance considerations</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Usage Guidelines */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-8">
          <h2 className="text-2xl font-semibold text-blue-900 mb-6">Usage Guidelines</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-blue-800 mb-4">Best Practices</h3>
              <ul className="space-y-2 text-blue-700">
                <li>• Use appropriate notification types for different scenarios</li>
                <li>• Include all relevant contact information</li>
                <li>• Specify clear deadlines and time-sensitive information</li>
                <li>• Provide actionable next steps</li>
                <li>• Maintain professional tone throughout</li>
                <li>• Test notifications across different devices</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-800 mb-4">Customization Options</h3>
              <ul className="space-y-2 text-blue-700">
                <li>• Adjust colors and branding to match organization</li>
                <li>• Modify contact information and details</li>
                <li>• Customize action buttons and links</li>
                <li>• Add or remove sections as needed</li>
                <li>• Integrate with existing notification systems</li>
                <li>• Implement automated delivery mechanisms</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationDemo;