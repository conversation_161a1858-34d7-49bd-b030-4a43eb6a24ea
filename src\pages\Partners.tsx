import React, { useState } from 'react';
import { ExternalLink, Filter } from 'lucide-react';
import { mockPartners } from '../data/mockData';
import { Partner } from '../types';

const Partners: React.FC = () => {
  const [selectedType, setSelectedType] = useState<string>('');
  
  const organizationTypes = ['NGO', 'Government', 'Corporate'];

  const filteredPartners = selectedType 
    ? mockPartners.filter(partner => partner.type === selectedType)
    : mockPartners;

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'NGO':
        return 'bg-blue-100 text-blue-800';
      case 'Government':
        return 'bg-green-100 text-green-800';
      case 'Corporate':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Partner Organizations
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Working together with trusted organizations to provide comprehensive disaster response and community support
          </p>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap items-center justify-center gap-4 mb-8">
          <div className="flex items-center space-x-2">
            <Filter size={20} className="text-gray-500" />
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-red-500 focus:border-red-500"
            >
              <option value="">All Organizations</option>
              {organizationTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Partner Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredPartners.map((partner) => (
            <div key={partner.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
              <div className="aspect-video overflow-hidden bg-gray-100">
                <img
                  src={partner.logo}
                  alt={`${partner.name} logo`}
                  className="w-full h-full object-cover"
                />
              </div>
              
              <div className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-xl font-semibold text-gray-900">{partner.name}</h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(partner.type)}`}>
                    {partner.type}
                  </span>
                </div>
                
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {partner.description}
                </p>
                
                <a
                  href={partner.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-red-600 hover:text-red-700 font-medium"
                >
                  Visit Website
                  <ExternalLink size={16} className="ml-1" />
                </a>
              </div>
            </div>
          ))}
        </div>

        {/* Partnership Info */}
        <div className="mt-16 bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Become a Partner
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Join our network of trusted organizations working to build resilient communities and provide effective disaster response.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 text-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">1</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Apply</h3>
              <p className="text-gray-600">
                Submit your organization's information and credentials for review by our partnership team.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 text-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">2</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Review</h3>
              <p className="text-gray-600">
                Our team evaluates your organization's capabilities and alignment with our mission and values.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 text-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">3</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Collaborate</h3>
              <p className="text-gray-600">
                Once approved, start collaborating on disaster response efforts and community building initiatives.
              </p>
            </div>
          </div>

          <div className="text-center mt-8">
            <button className="bg-red-600 text-white px-8 py-3 rounded-lg hover:bg-red-700 transition-colors font-medium">
              Apply for Partnership
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Partners;