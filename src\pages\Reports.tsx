import React, { useState, useMemo } from 'react';
import { Search, Filter, MapPin, Calendar, Eye, ChevronLeft, ChevronRight } from 'lucide-react';
import { mockReports } from '../data/mockData';
import { Report } from '../types';
import { format } from 'date-fns';
import ReportMap from '../components/Map/ReportMap';
import { Link } from 'react-router-dom';

const Reports: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDisasterType, setSelectedDisasterType] = useState('');
  const [selectedAssistanceType, setSelectedAssistanceType] = useState<string[]>([]);
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(6);
  const [showAssistanceDropdown, setShowAssistanceDropdown] = useState(false);

  const assistanceTypes = [
    'Emergency Shelter',
    'Food & Water',
    'Medical Aid',
    'Evacuation Support',
    'Pet Care',
    'Transportation',
    'Cleanup Volunteers',
    'Construction Help',
    'Financial Support'
  ];

  const disasterTypes = ['Flash Flood', 'Wildfire', 'Tornado', 'Earthquake', 'Hurricane'];

  const filteredReports = useMemo(() => {
    return mockReports.filter(report => {
      const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           report.location.address.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesDisasterType = selectedDisasterType === '' || report.disasterDetail === selectedDisasterType;
      
      const matchesAssistanceType = selectedAssistanceType.length === 0 ||
                                   selectedAssistanceType.some(type => report.assistanceNeeded.includes(type));

      return matchesSearch && matchesDisasterType && matchesAssistanceType;
    });
  }, [searchTerm, selectedDisasterType, selectedAssistanceType]);

  // Pagination calculations
  const totalItems = filteredReports.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
  const currentReports = filteredReports.slice(startIndex, endIndex);

  const handleAssistanceTypeChange = (type: string) => {
    setSelectedAssistanceType(prev => 
      prev.includes(type) 
        ? prev.filter(t => t !== type)
        : [...prev, type]
    );
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setSelectedReport(null); // Clear selected report when changing pages
  };

  const handleFilterChange = () => {
    setCurrentPage(1); // Reset to first page when filters change
  };

  const renderPaginationControls = () => {
    const pageNumbers = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    return (
      <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <label htmlFor="itemsPerPage" className="text-sm font-medium text-gray-700">
              Show:
            </label>
            <select
              id="itemsPerPage"
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="border border-gray-200 rounded-lg px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={6}>6</option>
              <option value={12}>12</option>
              <option value={18}>18</option>
              <option value={24}>24</option>
            </select>
          </div>
          <div className="text-sm text-gray-600">
            Showing {startIndex + 1}-{endIndex} of {totalItems} reports
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Previous page"
          >
            <ChevronLeft size={16} />
          </button>

          {startPage > 1 && (
            <>
              <button
                onClick={() => handlePageChange(1)}
                className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                1
              </button>
              {startPage > 2 && <span className="text-gray-400">...</span>}
            </>
          )}

          {pageNumbers.map((pageNum) => (
            <button
              key={pageNum}
              onClick={() => handlePageChange(pageNum)}
              className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                currentPage === pageNum
                  ? 'bg-blue-600 text-white'
                  : 'border border-gray-300 hover:bg-gray-50'
              }`}
            >
              {pageNum}
            </button>
          ))}

          {endPage < totalPages && (
            <>
              {endPage < totalPages - 1 && <span className="text-gray-400">...</span>}
              <button
                onClick={() => handlePageChange(totalPages)}
                className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                {totalPages}
              </button>
            </>
          )}

          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Next page"
          >
            <ChevronRight size={16} />
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Disaster Reports
              </h1>
              <p className="text-xl text-gray-600">
                View verified disaster reports and offer assistance to affected communities
              </p>
            </div>
            
            <div className="mt-4 md:mt-0">
              <div className="bg-white rounded-xl p-3 border border-gray-200 shadow-sm">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <MapPin size={16} className="text-blue-500" />
                  <span className="font-medium">Map View with Pagination</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search Location
              </label>
              <div className="relative">
                <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    handleFilterChange();
                  }}
                  placeholder="City, state, or region..."
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                />
              </div>
            </div>

            {/* Disaster Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Disaster Type
              </label>
              <select
                value={selectedDisasterType}
                onChange={(e) => {
                  setSelectedDisasterType(e.target.value);
                  handleFilterChange();
                }}
                className="w-full px-3 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              >
                <option value="">All Types</option>
                {disasterTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            {/* Assistance Needed */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Assistance Needed
              </label>
              <div className="relative">
                <button
                  type="button"
                  onClick={() => setShowAssistanceDropdown(!showAssistanceDropdown)}
                  className="w-full px-3 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-left flex items-center justify-between transition-colors"
                >
                  <span className="text-gray-700">
                    {selectedAssistanceType.length === 0 
                      ? 'All Types'
                      : `${selectedAssistanceType.length} selected`
                    }
                  </span>
                  <Filter size={16} className="text-gray-400" />
                </button>
                {showAssistanceDropdown && (
                  <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-xl shadow-lg z-10 max-h-40 overflow-y-auto">
                    {assistanceTypes.map(type => (
                      <label key={type} className="flex items-center px-3 py-2 hover:bg-gray-50 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedAssistanceType.includes(type)}
                          onChange={() => {
                            handleAssistanceTypeChange(type);
                            handleFilterChange();
                          }}
                          className="mr-2 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700">{type}</span>
                      </label>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Clear Filters */}
          {(searchTerm || selectedDisasterType || selectedAssistanceType.length > 0) && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedDisasterType('');
                  setSelectedAssistanceType([]);
                  setCurrentPage(1);
                }}
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                Clear all filters
              </button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Map */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Report Locations</h2>
            <ReportMap
              reports={currentReports}
              selectedReport={selectedReport}
              onReportSelect={setSelectedReport}
              height="500px"
            />
          </div>

          {/* Report List with Pagination */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-900">
                Reports ({totalItems})
              </h2>
              <div className="text-sm text-gray-500">
                Page {currentPage} of {totalPages}
              </div>
            </div>
            
            <div className="space-y-4 max-h-[500px] overflow-y-auto">
              {currentReports.length > 0 ? (
                currentReports.map(report => (
                  <div
                    key={report.id}
                    className={`p-4 border border-gray-200 rounded-xl cursor-pointer transition-all hover:shadow-md ${
                      selectedReport?.id === report.id ? 'border-blue-500 bg-blue-50' : 'hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedReport(report)}
                  >
                    <div className="flex items-start space-x-4">
                      <img
                        src={report.photos[0]}
                        alt={report.title}
                        className="w-16 h-16 object-cover rounded-xl"
                      />
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 mb-1">{report.title}</h3>
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">{report.description}</p>
                        <div className="flex items-center text-xs text-gray-500 space-x-4">
                          <div className="flex items-center">
                            <MapPin size={12} className="mr-1 text-blue-500" />
                            {report.location.address.split(',')[0]}
                          </div>
                          <div className="flex items-center">
                            <Calendar size={12} className="mr-1 text-green-500" />
                            {format(report.createdAt, 'MMM d')}
                          </div>
                        </div>
                        <div className="flex items-center justify-between mt-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            report.status === 'verified' ? 'bg-green-100 text-green-800' :
                            report.status === 'pending' ? 'bg-orange-100 text-orange-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                          </span>
                          <Link
                            to={`/reports/${report.id}`}
                            className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Eye size={14} className="mr-1" />
                            View
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MapPin size={24} className="text-gray-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No reports found</h3>
                  <p className="text-gray-600">Try adjusting your search criteria or filters.</p>
                </div>
              )}
            </div>

            {/* Pagination Controls */}
            {totalPages > 1 && renderPaginationControls()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Reports;