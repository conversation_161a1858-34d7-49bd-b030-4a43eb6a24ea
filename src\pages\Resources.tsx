import React, { useState } from 'react';
import { Search, Download, Printer as Print, BookOpen, AlertTriangle, Zap, Flame, Cloud, Wind, FileText, Eye, Share2, Filter, Clock, Users } from 'lucide-react';
import { mockSafetyGuides } from '../data/mockData';

const Resources: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedGuide, setSelectedGuide] = useState<any>(null);
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [showPrintModal, setShowPrintModal] = useState(false);

  const categories = [
    { name: 'Flood', icon: Cloud, color: 'bg-blue-50 text-blue-600 border-blue-200', activeColor: 'bg-blue-100 border-blue-300' },
    { name: 'Earthquake', icon: Zap, color: 'bg-amber-50 text-amber-600 border-amber-200', activeColor: 'bg-amber-100 border-amber-300' },
    { name: 'Fire', icon: Flame, color: 'bg-orange-50 text-orange-600 border-orange-200', activeColor: 'bg-orange-100 border-orange-300' },
    { name: 'Tornado', icon: Wind, color: 'bg-purple-50 text-purple-600 border-purple-200', activeColor: 'bg-purple-100 border-purple-300' },
    { name: 'General', icon: AlertTriangle, color: 'bg-gray-50 text-gray-600 border-gray-200', activeColor: 'bg-gray-100 border-gray-300' }
  ];

  // Mock expanded safety guides with enhanced data
  const safetyGuides = [
    {
      id: '1',
      title: 'Flood Safety and Preparedness Guide',
      category: 'Flood',
      description: 'Comprehensive guide covering flood preparation, safety during flooding, and post-flood recovery procedures.',
      content: 'Essential safety measures during flooding situations including evacuation procedures, water safety, and property protection.',
      lastUpdated: new Date('2024-01-01'),
      readTime: '8 min read',
      fileSize: '2.3 MB',
      pages: 12,
      downloadCount: 1247,
      difficulty: 'Beginner',
      tags: ['Emergency', 'Preparation', 'Safety']
    },
    {
      id: '2',
      title: 'Earthquake Emergency Response',
      category: 'Earthquake',
      description: 'Step-by-step instructions for earthquake preparedness and immediate response actions.',
      content: 'How to prepare for and respond to earthquakes including drop, cover, and hold techniques.',
      lastUpdated: new Date('2024-01-01'),
      readTime: '12 min read',
      fileSize: '3.1 MB',
      pages: 18,
      downloadCount: 892,
      difficulty: 'Intermediate',
      tags: ['Emergency', 'Response', 'Safety']
    },
    {
      id: '3',
      title: 'Wildfire Evacuation Planning',
      category: 'Fire',
      description: 'Planning and executing safe evacuation during wildfire emergencies.',
      content: 'Detailed evacuation procedures, go-bag preparation, and wildfire safety protocols.',
      lastUpdated: new Date('2024-01-01'),
      readTime: '10 min read',
      fileSize: '2.8 MB',
      pages: 15,
      downloadCount: 634,
      difficulty: 'Intermediate',
      tags: ['Evacuation', 'Planning', 'Wildfire']
    },
    {
      id: '4',
      title: 'Tornado Warning Systems and Safety',
      category: 'Tornado',
      description: 'Understanding tornado warnings and finding safe shelter during severe weather.',
      content: 'Tornado safety procedures, warning signs, and shelter identification.',
      lastUpdated: new Date('2024-01-01'),
      readTime: '6 min read',
      fileSize: '1.9 MB',
      pages: 10,
      downloadCount: 445,
      difficulty: 'Beginner',
      tags: ['Warning', 'Shelter', 'Weather']
    },
    {
      id: '5',
      title: 'Emergency Kit Essentials',
      category: 'General',
      description: 'Building and maintaining a comprehensive emergency preparedness kit.',
      content: 'Complete checklist for emergency supplies, food, water, and essential items.',
      lastUpdated: new Date('2024-01-01'),
      readTime: '15 min read',
      fileSize: '4.2 MB',
      pages: 24,
      downloadCount: 2156,
      difficulty: 'Beginner',
      tags: ['Preparation', 'Supplies', 'Checklist']
    },
    {
      id: '6',
      title: 'Family Emergency Communication Plan',
      category: 'General',
      description: 'Creating an effective communication strategy for family members during disasters.',
      content: 'How to establish contact methods, meeting points, and emergency contacts.',
      lastUpdated: new Date('2024-01-01'),
      readTime: '7 min read',
      fileSize: '1.7 MB',
      pages: 8,
      downloadCount: 789,
      difficulty: 'Beginner',
      tags: ['Communication', 'Family', 'Planning']
    }
  ];

  const filteredGuides = safetyGuides.filter(guide => {
    const matchesSearch = guide.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         guide.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         guide.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === '' || guide.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const handleDownload = (guide: any, format: string = 'pdf') => {
    const fileName = `${guide.title.replace(/\s+/g, '_')}_${new Date().getFullYear()}.${format}`;
    console.log(`Downloading: ${fileName} (${guide.fileSize})`);
    alert(`Download started: ${fileName}\n\nFile will be saved to your Downloads folder.\nSize: ${guide.fileSize}\nFormat: ${format.toUpperCase()}`);
    setShowDownloadModal(false);
  };

  const handlePrint = (guide: any, options: any = {}) => {
    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${guide.title}</title>
          <style>
            @media print {
              body { font-family: 'Inter', Arial, sans-serif; margin: 1in; line-height: 1.6; }
              h1 { color: #4A90E2; border-bottom: 2px solid #4A90E2; padding-bottom: 10px; }
              .header { text-align: center; margin-bottom: 30px; }
              .content { line-height: 1.6; }
              .footer { position: fixed; bottom: 0; width: 100%; text-align: center; font-size: 12px; color: #666; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${guide.title}</h1>
            <p><strong>Category:</strong> ${guide.category} | <strong>Updated:</strong> ${guide.lastUpdated.toLocaleDateString()}</p>
            <p><strong>DisasterWatch Safety Resources</strong></p>
          </div>
          <div class="content">
            <h2>Description</h2>
            <p>${guide.description}</p>
            <h2>Content</h2>
            <p>${guide.content}</p>
            <p><em>This is a preview. The full guide contains ${guide.pages} pages of detailed information.</em></p>
          </div>
          <div class="footer">
            <p>© 2024 DisasterWatch - Community Safety Resources | Page 1 of ${guide.pages}</p>
          </div>
        </body>
      </html>
    `;

    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    }

    setShowPrintModal(false);
  };

  const handleShare = async (guide: any) => {
    const shareData = {
      title: guide.title,
      text: guide.description,
      url: window.location.href
    };

    if (navigator.share) {
      try {
        await navigator.share(shareData);
      } catch (error) {
        if (error instanceof Error && (error.name === 'NotAllowedError' || error.name === 'AbortError')) {
          try {
            await navigator.clipboard.writeText(`${guide.title}\n${guide.description}\n${window.location.href}`);
            alert('Link copied to clipboard!');
          } catch (clipboardError) {
            console.error('Share and clipboard both failed:', error, clipboardError);
            alert('Unable to share. Please copy the URL manually.');
          }
        } else {
          console.error('Share failed:', error);
        }
      }
    } else {
      try {
        await navigator.clipboard.writeText(`${guide.title}\n${guide.description}\n${window.location.href}`);
        alert('Link copied to clipboard!');
      } catch (clipboardError) {
        console.error('Clipboard access failed:', clipboardError);
        alert('Unable to copy to clipboard. Please copy the URL manually.');
      }
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'Intermediate':
        return 'bg-amber-50 text-amber-700 border-amber-200';
      case 'Advanced':
        return 'bg-red-50 text-red-700 border-red-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-white border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Safety Resources & Guides
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              Essential safety information and preparedness guides to help you and your community stay safe during disasters
            </p>
          </div>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Search and Filter Section */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-12">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Search Bar */}
            <div className="flex-1">
              <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-3">
                Search Resources
              </label>
              <div className="relative">
                <Search size={20} className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  id="search"
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search guides, topics, or keywords..."
                  className="w-full pl-12 pr-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 placeholder-gray-500"
                />
              </div>
            </div>

            {/* Category Filter */}
            <div className="lg:w-64">
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-3">
                Category
              </label>
              <div className="relative">
                <Filter size={20} className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <select
                  id="category"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 appearance-none bg-white"
                >
                  <option value="">All Categories</option>
                  {categories.map(category => (
                    <option key={category.name} value={category.name}>{category.name}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Category Quick Navigation */}
        <div className="flex flex-wrap gap-3 mb-12 justify-center">
          {categories.map((category) => {
            const isActive = selectedCategory === category.name;
            return (
              <button
                key={category.name}
                onClick={() => setSelectedCategory(isActive ? '' : category.name)}
                className={`flex items-center space-x-3 px-6 py-3 rounded-xl border transition-all duration-200 hover:shadow-md transform hover:-translate-y-0.5 ${
                  isActive
                    ? `${category.activeColor} shadow-sm`
                    : `${category.color} hover:shadow-sm`
                }`}
              >
                <div className="p-1 rounded-lg bg-white/50">
                  <category.icon size={18} />
                </div>
                <span className="font-medium">{category.name}</span>
              </button>
            );
          })}
        </div>

        {/* Results Summary */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <h2 className="text-2xl font-semibold text-gray-900">
              {filteredGuides.length} {filteredGuides.length === 1 ? 'Guide' : 'Guides'} Found
            </h2>
            {(searchTerm || selectedCategory) && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('');
                }}
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                Clear filters
              </button>
            )}
          </div>
        </div>

        {/* Guides Grid */}
        {filteredGuides.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {filteredGuides.map((guide) => {
              const category = categories.find(cat => cat.name === guide.category);
              const CategoryIcon = category?.icon || BookOpen;
              
              return (
                <div 
                  key={guide.id} 
                  className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 group"
                >
                  {/* Card Header */}
                  <div className="p-6 pb-4">
                    <div className="flex items-start justify-between mb-4">
                      <div className={`p-3 rounded-xl ${category?.color || 'bg-gray-100 text-gray-600'}`}>
                        <CategoryIcon size={24} />
                      </div>
                      <div className="text-right">
                        <div className="flex items-center text-sm text-gray-500 mb-1">
                          <Clock size={14} className="mr-1" />
                          {guide.readTime}
                        </div>
                        <div className="flex items-center text-xs text-gray-400">
                          <FileText size={12} className="mr-1" />
                          {guide.pages} pages
                        </div>
                      </div>
                    </div>
                    
                    <h3 className="text-xl font-semibold text-gray-900 mb-3 leading-tight group-hover:text-blue-600 transition-colors">
                      {guide.title}
                    </h3>
                    
                    <p className="text-gray-600 mb-4 leading-relaxed line-clamp-3">
                      {guide.description}
                    </p>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {guide.tags.slice(0, 3).map((tag, index) => (
                        <span 
                          key={index}
                          className="px-2 py-1 bg-gray-100 text-gray-600 text-xs font-medium rounded-lg"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Card Footer */}
                  <div className="px-6 pb-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium border ${category?.color || 'bg-gray-100 text-gray-600'}`}>
                          {guide.category}
                        </span>
                        <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getDifficultyColor(guide.difficulty)}`}>
                          {guide.difficulty}
                        </span>
                      </div>
                      <div className="flex items-center text-xs text-gray-500">
                        <Users size={12} className="mr-1" />
                        {guide.downloadCount.toLocaleString()}
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <button 
                        onClick={() => setSelectedGuide(guide)}
                        className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-4 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium flex items-center justify-center shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
                      >
                        <Eye size={16} className="mr-2" />
                        Read Guide
                      </button>
                      
                      <div className="grid grid-cols-3 gap-2">
                        <button
                          onClick={() => {
                            setSelectedGuide(guide);
                            setShowDownloadModal(true);
                          }}
                          className="flex items-center justify-center py-2 px-3 border border-gray-200 text-gray-600 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 text-sm font-medium"
                        >
                          <Download size={14} className="mr-1" />
                          Download
                        </button>
                        <button
                          onClick={() => {
                            setSelectedGuide(guide);
                            setShowPrintModal(true);
                          }}
                          className="flex items-center justify-center py-2 px-3 border border-gray-200 text-gray-600 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 text-sm font-medium"
                        >
                          <Print size={14} className="mr-1" />
                          Print
                        </button>
                        <button
                          onClick={() => handleShare(guide)}
                          className="flex items-center justify-center py-2 px-3 border border-gray-200 text-gray-600 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200"
                        >
                          <Share2 size={14} />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <BookOpen size={32} className="text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No guides found</h3>
            <p className="text-gray-600 mb-6">Try adjusting your search terms or category filter.</p>
            <button
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('');
              }}
              className="bg-blue-500 text-white px-6 py-3 rounded-xl hover:bg-blue-600 transition-colors font-medium"
            >
              Show All Guides
            </button>
          </div>
        )}

        {/* Download Modal */}
        {showDownloadModal && selectedGuide && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full transform transition-all">
              <div className="p-8">
                <h3 className="text-2xl font-semibold text-gray-900 mb-6">Download Guide</h3>
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-2">{selectedGuide.title}</h4>
                  <p className="text-sm text-gray-600">File size: {selectedGuide.fileSize} • {selectedGuide.pages} pages</p>
                </div>
                
                <div className="space-y-4 mb-8">
                  <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                    <h5 className="font-medium text-blue-900 mb-2">📋 Download Instructions</h5>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• File will be saved to your Downloads folder</li>
                      <li>• No login required for public safety guides</li>
                      <li>• Compatible with all PDF readers</li>
                      <li>• Recommended: Save with date for reference</li>
                    </ul>
                  </div>
                  
                  <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                    <h5 className="font-medium text-green-900 mb-2">💾 File Naming Suggestion</h5>
                    <p className="text-sm text-green-800 font-mono">
                      {selectedGuide.title.replace(/\s+/g, '_')}_2024.pdf
                    </p>
                  </div>
                </div>

                <div className="space-y-3">
                  <button
                    onClick={() => handleDownload(selectedGuide, 'pdf')}
                    className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-4 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium flex items-center justify-center shadow-sm"
                  >
                    <Download size={16} className="mr-2" />
                    Download PDF ({selectedGuide.fileSize})
                  </button>
                  <button
                    onClick={() => handleDownload(selectedGuide, 'epub')}
                    className="w-full border border-gray-200 text-gray-700 py-3 px-4 rounded-xl hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 font-medium"
                  >
                    Download EPUB (Mobile-friendly)
                  </button>
                </div>

                <button
                  onClick={() => setShowDownloadModal(false)}
                  className="w-full mt-4 border border-gray-200 text-gray-700 py-3 px-4 rounded-xl hover:bg-gray-50 transition-colors font-medium"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Print Modal */}
        {showPrintModal && selectedGuide && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl shadow-2xl max-w-lg w-full transform transition-all">
              <div className="p-8">
                <h3 className="text-2xl font-semibold text-gray-900 mb-6">Print Settings</h3>
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-2">{selectedGuide.title}</h4>
                  <p className="text-sm text-gray-600">{selectedGuide.pages} pages</p>
                </div>

                <div className="space-y-6 mb-8">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Paper Size</label>
                      <select className="w-full px-3 py-3 border border-gray-200 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="A4">A4 (8.3 × 11.7 in)</option>
                        <option value="Letter">Letter (8.5 × 11 in)</option>
                        <option value="Legal">Legal (8.5 × 14 in)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Orientation</label>
                      <select className="w-full px-3 py-3 border border-gray-200 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="portrait">Portrait</option>
                        <option value="landscape">Landscape</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Color Mode</label>
                      <select className="w-full px-3 py-3 border border-gray-200 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="color">Color</option>
                        <option value="grayscale">Grayscale</option>
                        <option value="blackwhite">Black & White</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Quality</label>
                      <select className="w-full px-3 py-3 border border-gray-200 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="high">High (600 DPI)</option>
                        <option value="medium">Medium (300 DPI)</option>
                        <option value="draft">Draft (150 DPI)</option>
                      </select>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <input type="checkbox" id="doubleSided" className="mr-3 rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                    <label htmlFor="doubleSided" className="text-sm text-gray-700">
                      Double-sided printing (saves paper)
                    </label>
                  </div>

                  <div className="bg-amber-50 border border-amber-200 rounded-xl p-4">
                    <h5 className="font-medium text-amber-900 mb-2">🖨️ Print Tips</h5>
                    <ul className="text-sm text-amber-800 space-y-1">
                      <li>• Use high-quality paper for better readability</li>
                      <li>• Check printer ink levels before printing</li>
                      <li>• Consider printing key pages only to save resources</li>
                    </ul>
                  </div>
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={() => handlePrint(selectedGuide)}
                    className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-4 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium flex items-center justify-center shadow-sm"
                  >
                    <Print size={16} className="mr-2" />
                    Print Now
                  </button>
                  <button
                    onClick={() => setShowPrintModal(false)}
                    className="flex-1 border border-gray-200 text-gray-700 py-3 px-4 rounded-xl hover:bg-gray-50 transition-colors font-medium"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Guide Viewer Modal */}
        {selectedGuide && !showDownloadModal && !showPrintModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full h-[90vh] flex flex-col transform transition-all">
              <div className="flex items-center justify-between p-8 border-b border-gray-100">
                <div>
                  <h3 className="text-2xl font-semibold text-gray-900 mb-1">{selectedGuide.title}</h3>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span>{selectedGuide.category}</span>
                    <span>•</span>
                    <span>{selectedGuide.readTime}</span>
                    <span>•</span>
                    <span>{selectedGuide.difficulty}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowDownloadModal(true)}
                    className="p-3 border border-gray-200 text-gray-600 rounded-xl hover:bg-gray-50 transition-colors"
                    title="Download"
                  >
                    <Download size={18} />
                  </button>
                  <button
                    onClick={() => setShowPrintModal(true)}
                    className="p-3 border border-gray-200 text-gray-600 rounded-xl hover:bg-gray-50 transition-colors"
                    title="Print"
                  >
                    <Print size={18} />
                  </button>
                  <button
                    onClick={() => setSelectedGuide(null)}
                    className="p-3 border border-gray-200 text-gray-600 rounded-xl hover:bg-gray-50 transition-colors"
                  >
                    ✕
                  </button>
                </div>
              </div>
              <div className="flex-1 overflow-y-auto p-8">
                <div className="prose max-w-none">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Overview</h2>
                  <p className="text-gray-700 leading-relaxed mb-6">{selectedGuide.description}</p>
                  
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Content Preview</h2>
                  <p className="text-gray-700 leading-relaxed mb-6">{selectedGuide.content}</p>
                  
                  <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 mt-8">
                    <div className="flex items-start space-x-3">
                      <BookOpen size={20} className="text-blue-600 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-blue-900 mb-2">📖 Full Guide Available</h4>
                        <p className="text-blue-800 text-sm leading-relaxed">
                          This preview shows the first section. Download the complete {selectedGuide.pages}-page guide for comprehensive safety information, detailed procedures, and helpful illustrations.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Emergency Contacts Section */}
        <div className="bg-gradient-to-br from-orange-50 to-red-50 border border-orange-200 rounded-2xl p-8">
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertTriangle size={32} />
            </div>
            <h2 className="text-3xl font-bold text-orange-900 mb-3">Emergency Contacts</h2>
            <p className="text-orange-700 text-lg">Keep these numbers readily available</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-xl p-6 border border-orange-200 text-center shadow-sm hover:shadow-md transition-shadow">
              <h3 className="font-semibold text-orange-900 mb-3 text-lg">Emergency Services</h3>
              <p className="text-3xl font-bold text-orange-600 mb-2">911</p>
              <p className="text-sm text-orange-700">Fire, Police, Medical</p>
            </div>
            
            <div className="bg-white rounded-xl p-6 border border-orange-200 text-center shadow-sm hover:shadow-md transition-shadow">
              <h3 className="font-semibold text-orange-900 mb-3 text-lg">Poison Control</h3>
              <p className="text-xl font-bold text-orange-600 mb-2">1-800-222-1222</p>
              <p className="text-sm text-orange-700">24/7 Poison Help</p>
            </div>
            
            <div className="bg-white rounded-xl p-6 border border-orange-200 text-center shadow-sm hover:shadow-md transition-shadow">
              <h3 className="font-semibold text-orange-900 mb-3 text-lg">Red Cross</h3>
              <p className="text-xl font-bold text-orange-600 mb-2">1-800-RED-CROSS</p>
              <p className="text-sm text-orange-700">Disaster Relief</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Resources;