export interface AdminUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'admin' | 'editor' | 'viewer';
  permissions: Permission[];
  lastLogin: Date;
  isActive: boolean;
  createdAt: Date;
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete';
}

export interface ContentSection {
  id: string;
  type: 'hero' | 'features' | 'testimonials' | 'pricing' | 'stats' | 'partners';
  title: string;
  content: any;
  isPublished: boolean;
  scheduledAt?: Date;
  lastModified: Date;
  modifiedBy: string;
  version: number;
}

export interface AnalyticsData {
  visitors: {
    total: number;
    unique: number;
    returning: number;
    trend: number;
  };
  engagement: {
    avgSessionDuration: number;
    bounceRate: number;
    pageViews: number;
    trend: number;
  };
  conversions: {
    signups: number;
    reports: number;
    rate: number;
    trend: number;
  };
  traffic: {
    sources: Array<{ name: string; value: number; color: string }>;
    devices: Array<{ name: string; value: number }>;
    locations: Array<{ country: string; visitors: number }>;
  };
}

export interface DashboardMetrics {
  totalReports: number;
  verifiedReports: number;
  activeUsers: number;
  responseTime: string;
  systemHealth: 'excellent' | 'good' | 'warning' | 'critical';
  recentActivity: ActivityLog[];
}

export interface ActivityLog {
  id: string;
  action: string;
  user: string;
  timestamp: Date;
  details: string;
  type: 'content' | 'user' | 'system' | 'security';
}