export interface ChatMessage {
  id: string;
  content: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  roomId: string;
  type: 'text' | 'image' | 'file';
  timestamp: Date;
  read: boolean;
  edited?: boolean;
  fileUrl?: string;
  fileSize?: number;
  reactions: MessageReaction[];
}

export interface MessageReaction {
  emoji: string;
  userId: string;
  userName: string;
}

export interface ChatUser {
  id: string;
  name: string;
  avatar?: string;
  isOnline: boolean;
  role: 'admin' | 'moderator' | 'user';
  lastSeen: Date;
  isTyping: boolean;
}

export interface ChatRoom {
  id: string;
  name: string;
  description: string;
  memberCount: number;
  isPrivate: boolean;
}

export interface TypingIndicator {
  userId: string;
  userName: string;
  roomId: string;
  timestamp: Date;
}