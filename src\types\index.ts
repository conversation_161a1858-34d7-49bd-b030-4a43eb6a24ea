export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

export interface Report {
  id: string;
  title: string;
  disasterType: 'Natural' | 'Non-Natural';
  disasterDetail: string;
  description: string;
  location: {
    lat: number;
    lng: number;
    address: string;
  };
  photos: string[];
  assistanceNeeded: string[];
  assistanceDescription: string;
  status: 'pending' | 'verified' | 'rejected';
  reporterId: string;
  reporterName: string;
  createdAt: Date;
  updatedAt: Date;
  assistanceLog: AssistanceEntry[];
}

export interface AssistanceEntry {
  id: string;
  providerId: string;
  providerName: string;
  description: string;
  createdAt: Date;
  endorsed: boolean;
}

export interface Partner {
  id: string;
  name: string;
  type: 'NGO' | 'Government' | 'Corporate';
  description: string;
  website: string;
  logo: string;
}

export interface SafetyGuide {
  id: string;
  title: string;
  category: string;
  content: string;
  lastUpdated: Date;
}